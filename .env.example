# Blue Bingo Bot Environment Configuration
# Copy this file to .env and fill in your actual values

# Development mode (set to true for local development)
DEV=false

# Server configuration
PORT=443

# Telegram Bot Configuration
TELEGRAM_TOKEN=your_telegram_bot_token_here
SUPPORT_TELEGRAM_TOKEN=your_support_bot_token_here

# Set to true to disable telegram bots (for testing)
KILL_TELEGRAM_BOTS=false

# MongoDB Configuration
MONGO_USER=your_mongodb_username
MONGO_PASSWORD=your_mongodb_password

# Chapa Payment Gateway Configuration
CHAPA_PRIVATE_KEY=your_chapa_private_key
CHAPA_SECRET_HASH=your_chapa_secret_hash

# Game Configuration (optional - defaults will be used if not set)
# STARTING_BALANCE=0
# REFERRAL_PERCENTAGE=0.1
