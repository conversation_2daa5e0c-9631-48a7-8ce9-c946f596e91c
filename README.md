# Blue Bingo Bot

A Telegram-based Bingo game bot with real money transactions, built with Node.js, Express, and MongoDB.

## Features

### 🎮 Game Features
- **Real-time Bingo Games**: Play live Bingo games with other players
- **Multiple Stake Levels**: 10, 20, 50, 100 ETB and free demo mode
- **Automated Bot Players**: AI players to ensure games always have participants
- **Live Game Updates**: Real-time WebSocket connections for game state updates
- **Win Detection**: Automatic pattern detection and winner selection

### 💰 Financial Features
- **Real Money Transactions**: Deposit and withdraw real Ethiopian Birr (ETB)
- **Chapa Payment Integration**: Secure payment processing
- **Balance Management**: Main balance and bonus balance tracking
- **Referral System**: Earn 10% of referred players' winnings
- **Transaction History**: Complete audit trail of all transactions

### 👥 User Management
- **User Registration**: Phone number verification
- **Profile Management**: Name and contact information
- **Game Statistics**: Track wins, losses, and earnings
- **Admin Controls**: Comprehensive admin panel for user management

### 🛡️ Security & Reliability
- **Telegram Web App Authentication**: Secure user verification
- **Database Optimization**: Indexed queries and connection pooling
- **Error Handling**: Comprehensive error logging and recovery
- **Rate Limiting**: Protection against spam and abuse

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- MongoDB database
- Telegram Bot Token
- Chapa payment gateway account
- SSL certificate for HTTPS

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bingobot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

4. **Set up SSL certificates**
   - Place your SSL certificate files in the `cert/` directory:
     - `cert/certificate.crt`
     - `cert/private.key`

5. **Create fake names file**
   ```bash
   # Create fake_names.txt with bot usernames (one per line)
   echo '["Bot Player 1", "Bot Player 2", "Bot Player 3"]' > fake_names.txt
   ```

6. **Start the application**
   ```bash
   npm start
   ```

### Environment Variables

See `.env.example` for all required environment variables.

## Game Configuration

### Bot Behavior
- **Win Rates**: Configurable bot win percentages per stake level
- **Density Matrix**: Time-based bot player density
- **Minimum Density**: Minimum number of bot players

### Admin Commands (Support Bot)
- `/stats` - View system statistics
- `/chapastats` - View Chapa payment statistics
- `/settings` - View current bot settings
- `/setwinrate [stake] [rate]` - Set bot win rate (0-1)
- `/setdensity [stake] [multiplier]` - Set bot density multiplier
- `/setmindensity [count]` - Set minimum bot density
- `/addbalance [chatId] [amount]` - Add balance to user
- `/addbonus [chatId] [amount]` - Add bonus balance to user
- `/sendmsg [chatId] [message]` - Send message to user
- `/userinfo [chatId]` - Get user information
- `/permaban [chatId]` - Ban/unban user
- `/restart` - Restart the bot (when no active games)
- `/respawn` - Respawn disconnected games

## API Endpoints

- `GET /` - Instructions page
- `GET /bingo` - Game page
- `GET /choose-board` - Board selection page
- `GET /instructions` - How to play page
- `GET /ping` - Health check
- `POST /chapa` - Chapa webhook for payments
- `POST /chapa-approval` - Chapa approval webhook

## Architecture

### Backend Components
- **Express Server**: Web server and API endpoints
- **WebSocket Server**: Real-time game communication
- **MongoDB**: User data, games, and transactions
- **Telegram Bot API**: User interaction and notifications

### Frontend Components
- **Handlebars Templates**: Server-side rendering
- **Telegram Web App**: Game interface
- **WebSocket Client**: Real-time updates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For technical support or questions, contact the development team or use the in-app support feature.
