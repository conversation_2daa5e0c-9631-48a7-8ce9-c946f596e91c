Express server is listening on 443
Express server is listening on 443
Unhandled rejection Error: ETELEGRAM: 409 Conflict: terminated by other setWebhook
    at /home/<USER>/bingobot/node_modules/node-telegram-bot-api/src/telegram.js:316:15
    at tryCatcher (/home/<USER>/bingobot/node_modules/bluebird/js/release/util.js:16:23)
    at Promise._settlePromiseFromHandler (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:547:31)
    at Promise._settlePromise (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:604:18)
    at Promise._settlePromise0 (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:649:10)
    at Promise._settlePromises (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:729:18)
    at _drainQueueStep (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:93:12)
    at _drainQueue (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:86:9)
    at Async._drainQueues (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:102:5)
    at Immediate.Async.drainQueues [as _onImmediate] (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:15:14)
    at processImmediate (node:internal/timers:466:21)
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSEauL2f&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724488502&hash=b08c3d6c4f4d7dc6bd5c69ae2daf38304a2905c70816ff544638465ffc808c11',
  page: '/choose-board',
  stake: 0
}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSEauL2f&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724488502&hash=b08c3d6c4f4d7dc6bd5c69ae2daf38304a2905c70816ff544638465ffc808c11',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGOA9Un&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489043&hash=f3baf40126932dd4d0c4f92f4bcabfa3ff2c9887c875447198b3c2cf8faa97c3',
  page: '/choose-board',
  stake: 0
}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSF4gcke&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489183&hash=abb624e64394f9c32daf5876e27fc8720dad1906281f6ff0782a5964b3c56bbe',
  page: '/choose-board',
  stake: 0
}
{ selected: 35 }
{ selected: 46 }
{ join: '66c99ddd6db751d7eab16928' }
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSF4gcke&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489183&hash=abb624e64394f9c32daf5876e27fc8720dad1906281f6ff0782a5964b3c56bbe',
  page: '/bingo',
  stake: 0,
  gameId: '66c99ddd6db751d7eab16928'
}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSEzBKTS&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489334&hash=742e9d6a6bc7d94ebf57bf1e59b2538a55dc17a42c676169bbc7e812ffe1763f',
  page: '/choose-board',
  stake: 0
}
{ join: '66c99e7178971b06bd332e06' }
{ selected: 96 }
{ join: '66c99e7178971b06bd332e06' }
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSEzBKTS&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489334&hash=742e9d6a6bc7d94ebf57bf1e59b2538a55dc17a42c676169bbc7e812ffe1763f',
  page: '/bingo',
  stake: 0,
  gameId: '66c99e7178971b06bd332e06'
}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSEzBKTS&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489334&hash=742e9d6a6bc7d94ebf57bf1e59b2538a55dc17a42c676169bbc7e812ffe1763f',
  page: '/bingo',
  stake: 0,
  gameId: '66c99e7178971b06bd332e06'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHiutIM&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489485&hash=b8f446a5f6f75493b07a383cf3c0a673af4aeb070f40b1956c72c05184cc0d17',
  page: '/choose-board',
  stake: 0
}
{ selected: 66 }
{ join: '66c99f09d93672e3f9cdac89' }
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHiutIM&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724489485&hash=b8f446a5f6f75493b07a383cf3c0a673af4aeb070f40b1956c72c05184cc0d17',
  page: '/bingo',
  stake: 0,
  gameId: '66c99f09d93672e3f9cdac89'
}
Express server is listening on 443
(node:223520) [node-telegram-bot-api] DeprecationWarning: In the future, content-type of files you send will default to "application/octet-stream". See https://github.com/yagop/node-telegram-bot-api/blob/master/doc/usage.md#sending-files for more information on how sending files has been improved and on how to disable this deprecation message altogether.
(Use `node --trace-deprecation ...` to show where the warning was created)
Unhandled rejection Error: ETELEGRAM: 409 Conflict: terminated by other setWebhook
    at /home/<USER>/bingobot/node_modules/node-telegram-bot-api/src/telegram.js:316:15
    at tryCatcher (/home/<USER>/bingobot/node_modules/bluebird/js/release/util.js:16:23)
    at Promise._settlePromiseFromHandler (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:547:31)
    at Promise._settlePromise (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:604:18)
    at Promise._settlePromise0 (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:649:10)
    at Promise._settlePromises (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:729:18)
    at _drainQueueStep (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:93:12)
    at _drainQueue (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:86:9)
    at Async._drainQueues (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:102:5)
    at Immediate.Async.drainQueues [as _onImmediate] (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:15:14)
    at processImmediate (node:internal/timers:466:21)
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSFnFK7s&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500056&hash=ca110c24fe0d7508b68822fa5ccecd5d2a1b548b8d28b4cbdefd27e1ceae5bbf',
  page: '/choose-board',
  stake: 0
}
{ selected: 1 }
{
  auth: 'query_id=AAG81BoRAAAAALzUGhFmnkGj&user=%7B%22id%22%3A286971068%2C%22first_name%22%3A%22Amir%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22User_Amirkedir%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500097&hash=ef792383ef5adf0854f29d770bd60a2fa78ad765e531dc63c852d304ef9475dd',
  page: '/choose-board',
  stake: 0
}
{ selected: 25 }
{ selected: 62 }
{ selected: 77 }
{ selected: 5 }
{ join: '66c9c85640615136571345b9' }
{ join: '66c9c85640615136571345b9' }
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSFnFK7s&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500056&hash=ca110c24fe0d7508b68822fa5ccecd5d2a1b548b8d28b4cbdefd27e1ceae5bbf',
  page: '/bingo',
  stake: 0,
  gameId: '66c9c85640615136571345b9'
}
{
  auth: 'query_id=AAG81BoRAAAAALzUGhFmnkGj&user=%7B%22id%22%3A286971068%2C%22first_name%22%3A%22Amir%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22User_Amirkedir%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500097&hash=ef792383ef5adf0854f29d770bd60a2fa78ad765e531dc63c852d304ef9475dd',
  page: '/bingo',
  stake: 0,
  gameId: '66c9c85640615136571345b9'
}
{ bingo: true, gameId: '66c9c85640615136571345b9', selectedNumber: 77 }
[
  51, 20, 27,  52, 71, 31, 58, 5,  75,
  13, 28, 55,  17, 22, 37, 54, 53, 32,
  8,  34, 48,  7,  64, 44, 45, 69, 49,
  67, 59, '*'
] [
  [ 10, 6, 59, 50, 62 ],
  [ 28, 33, 42, 56, 61 ],
  [ 22, 20, '*', 2, 71 ],
  [ 7, 67, 40, 51, 14 ],
  [ 40, 47, 35, 33, 14 ]
]
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSFnFK7s&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500056&hash=ca110c24fe0d7508b68822fa5ccecd5d2a1b548b8d28b4cbdefd27e1ceae5bbf',
  page: '/choose-board',
  stake: 0
}
errrr: TypeError: Cannot read properties of undefined (reading 'selected')
{ selected: 66 }
{ join: '66c9c85640615136571345b9' }
{ bingo: true, gameId: '66c9c85640615136571345b9', selectedNumber: 5 }
[
  51, 20, 27, 52, 71, 31, 58,  5,  75, 13,
  28, 55, 17, 22, 37, 54, 53,  32, 8,  34,
  48, 7,  64, 44, 45, 69, 49,  67, 59, '*',
  74, 65, 14, 38, 21, 42, '*'
] [
  [ 60, 41, 71, 71, 11 ],
  [ 19, 73, 71, 16, 40 ],
  [ 24, 36, '*', 58, 5 ],
  [ 8, 6, 71, 25, 44 ],
  [ 6, 69, 45, 69, 11 ]
]
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSFnFK7s&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500056&hash=ca110c24fe0d7508b68822fa5ccecd5d2a1b548b8d28b4cbdefd27e1ceae5bbf',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSFnFK7s&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724500056&hash=ca110c24fe0d7508b68822fa5ccecd5d2a1b548b8d28b4cbdefd27e1ceae5bbf',
  page: '/choose-board',
  stake: 10
}
Express server is listening on 443
(node:223841) [node-telegram-bot-api] DeprecationWarning: In the future, content-type of files you send will default to "application/octet-stream". See https://github.com/yagop/node-telegram-bot-api/blob/master/doc/usage.md#sending-files for more information on how sending files has been improved and on how to disable this deprecation message altogether.
(Use `node --trace-deprecation ...` to show where the warning was created)
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSERLcnd&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724506843&hash=70e0d5420196003a2d00aa1dba4abd92aa79463b03b067fa11cb8cc9253d12cd',
  page: '/choose-board',
  stake: 0
}
Express server is listening on 443
Express server is listening on 443
Express server is listening on 443
Express server is listening on 80
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
Express server is listening on 443
Unhandled rejection Error: ETELEGRAM: 409 Conflict: terminated by other setWebhook
    at /home/<USER>/bingobot/node_modules/node-telegram-bot-api/src/telegram.js:316:15
    at tryCatcher (/home/<USER>/bingobot/node_modules/bluebird/js/release/util.js:16:23)
    at Promise._settlePromiseFromHandler (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:547:31)
    at Promise._settlePromise (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:604:18)
    at Promise._settlePromise0 (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:649:10)
    at Promise._settlePromises (/home/<USER>/bingobot/node_modules/bluebird/js/release/promise.js:729:18)
    at _drainQueueStep (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:93:12)
    at _drainQueue (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:86:9)
    at Async._drainQueues (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:102:5)
    at Immediate.Async.drainQueues [as _onImmediate] (/home/<USER>/bingobot/node_modules/bluebird/js/release/async.js:15:14)
    at processImmediate (node:internal/timers:466:21)
Express server is listening on 443
Express server is listening on 443
Express server is listening on 443
node:events:491
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::443
    at Server.setupListenHandle [as _listen2] (node:net:1463:16)
    at listenInCluster (node:net:1511:12)
    at Server.listen (node:net:1599:7)
    at Object.<anonymous> (/home/<USER>/bingobot/server.js:126:9)
    at Module._compile (node:internal/modules/cjs/loader:1198:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1252:10)
    at Module.load (node:internal/modules/cjs/loader:1076:32)
    at Function.Module._load (node:internal/modules/cjs/loader:911:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:81:12)
    at node:internal/main/run_main_module:22:47
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1490:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 443
}
Express server is listening on 443
Express server is listening on 443
node:events:491
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::443
    at Server.setupListenHandle [as _listen2] (node:net:1463:16)
    at listenInCluster (node:net:1511:12)
    at Server.listen (node:net:1599:7)
    at Object.<anonymous> (/home/<USER>/bingobot/server.js:126:9)
    at Module._compile (node:internal/modules/cjs/loader:1198:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1252:10)
    at Module.load (node:internal/modules/cjs/loader:1076:32)
    at Function.Module._load (node:internal/modules/cjs/loader:911:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:81:12)
    at node:internal/main/run_main_module:22:47
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1490:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 443
}
Express server is listening on 443
BadRequestError: request aborted
    at IncomingMessage.onAborted (/home/<USER>/bingobot/node_modules/raw-body/index.js:245:10)
    at IncomingMessage.emit (node:events:513:28)
    at IncomingMessage._destroy (node:_http_incoming:224:10)
    at _destroy (node:internal/streams/destroy:102:25)
    at IncomingMessage.destroy (node:internal/streams/destroy:64:5)
    at abortIncoming (node:_http_server:642:9)
    at socketOnClose (node:_http_server:636:3)
    at TLSSocket.emit (node:events:525:35)
    at node:net:301:12
    at Socket.done (node:_tls_wrap:588:7)
    at Object.onceWrapper (node:events:628:26)
    at Socket.emit (node:events:513:28)
    at TCP.<anonymous> (node:net:301:12)
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjudG_4&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512101&hash=0c8e33805c76ed3a00255fd4058a390d2f411afc20adf34f0eb88fa8163dffb5',
  page: '/choose-board',
  stake: 50
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHW4fsN&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512100&hash=1f9d6a2eed7ef11891f2b784e2d7555beefdc3a0c70c7dbba47c256ef2beea24',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjudG_4&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512101&hash=0c8e33805c76ed3a00255fd4058a390d2f411afc20adf34f0eb88fa8163dffb5',
  page: '/choose-board',
  stake: 50
}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjUOJLf&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512204&hash=34d9ccd4faf418b4d0e5daf9b519b2d8cc618144d5813e6e36d6ed31a1c8358f',
  page: '/choose-board',
  stake: 100
}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjudG_4&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512101&hash=0c8e33805c76ed3a00255fd4058a390d2f411afc20adf34f0eb88fa8163dffb5',
  page: '/choose-board',
  stake: 50
}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjUOJLf&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512204&hash=34d9ccd4faf418b4d0e5daf9b519b2d8cc618144d5813e6e36d6ed31a1c8358f',
  page: '/choose-board',
  stake: 100
}
error: [polling_error] {"code":"ETELEGRAM","message":"ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running"}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjudG_4&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512101&hash=0c8e33805c76ed3a00255fd4058a390d2f411afc20adf34f0eb88fa8163dffb5',
  page: '/choose-board',
  stake: 50
}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjUOJLf&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512204&hash=34d9ccd4faf418b4d0e5daf9b519b2d8cc618144d5813e6e36d6ed31a1c8358f',
  page: '/choose-board',
  stake: 100
}
{
  auth: 'query_id=AAFJHVUYAAAAAEkdVRjudG_4&user=%7B%22id%22%3A408231241%2C%22first_name%22%3A%22Noel%20Cephalophore%22%2C%22last_name%22%3A%22%3F%22%2C%22username%22%3A%22the_animaniac%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512101&hash=0c8e33805c76ed3a00255fd4058a390d2f411afc20adf34f0eb88fa8163dffb5',
  page: '/choose-board',
  stake: 50
}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHOVe9v&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724512863&hash=b570229df28f8ca903e67b1a8756b51d5cce2a377ada9ff4ae8b8b9f91c7ef0e',
  page: '/choose-board',
  stake: 0
}
{ selected: 100 }
{ selected: 89 }
{ selected: 56 }
{ selected: 32 }
{ selected: 21 }
{ selected: 13 }
{ join: '66c9fa4644aef7a0c6571b5b' }
error: [polling_error] {"code":"EFATAL","message":"EFATAL: Error: read ECONNRESET"}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdA2LjT&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724548108&hash=17911619b464b2332825be4bb66f42300160440323d13510afd0fa6db4a61562',
  page: '/choose-board',
  stake: 0
}
{ selected: 100 }
{ join: '66c9fa4644aef7a0c6571b5b' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdA2LjT&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724548108&hash=17911619b464b2332825be4bb66f42300160440323d13510afd0fa6db4a61562',
  page: '/bingo',
  stake: 0,
  gameId: '66c9fa4644aef7a0c6571b5b'
}
{
  bingo: true,
  gameId: '66c9fa4644aef7a0c6571b5b',
  selectedNumber: 100
}
[ 71, 45, 38, 41, '*' ] [
  [ 38, 67, 57, 34, 28 ],
  [ 74, 10, 62, 29, 43 ],
  [ 3, 59, '*', 59, 65 ],
  [ 72, 7, 55, 69, 39 ],
  [ 33, 63, 57, 50, 52 ]
]
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdA2LjT&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724548108&hash=17911619b464b2332825be4bb66f42300160440323d13510afd0fa6db4a61562',
  page: '/choose-board',
  stake: 0
}
{ selected: 69 }
{ selected: 72 }
{ selected: 60 }
{ selected: 77 }
{ selected: 87 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdA2LjT&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724548108&hash=17911619b464b2332825be4bb66f42300160440323d13510afd0fa6db4a61562',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdA2LjT&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724548108&hash=17911619b464b2332825be4bb66f42300160440323d13510afd0fa6db4a61562',
  page: '/choose-board',
  stake: 0
}
{ selected: 89 }
(node:237777) [node-telegram-bot-api] DeprecationWarning: In the future, content-type of files you send will default to "application/octet-stream". See https://github.com/yagop/node-telegram-bot-api/blob/master/doc/usage.md#sending-files for more information on how sending files has been improved and on how to disable this deprecation message altogether.
(Use `node --trace-deprecation ...` to show where the warning was created)
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/choose-board',
  stake: 0
}
{ selected: 1 }
{ join: '66ca85a744aef7a0c6571b9b' }
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSHQrmmh&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724561289&hash=e64cf50d0a698e9bd6021fae7d8e5a71c39f6bdfd5ff5b11bebf835c781e1c12',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAHfRMIpAAAAAN9Ewinp9M_v&user=%7B%22id%22%3A700597471%2C%22first_name%22%3A%22Al%20saeed%20%F0%9F%A5%B6%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22al_saeed24%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724562791&hash=5b824c099e056e89ab3bebd1d888357df6b80afb101ee04b48e152242abf8588',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{ selected: 66 }
{ join: '66c9fa4644aef7a0c6571b5c' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBe6nVrp&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571802&hash=483f68a6688dc616d2be433f912e41e1aa2ecec5d63e873ba640b0f7643fe477',
  page: '/choose-board',
  stake: 10
}
{ selected: 17 }
{ join: '66c9fa4644aef7a0c6571b5c' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeJplqq&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571818&hash=d4002befd706c4a18eafc28bb538940bf9cad62d1829a327e5b0c96f62a620a3',
  page: '/choose-board',
  stake: 0
}
{ selected: 46 }
{ join: '66ca85a744aef7a0c6571b9b' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeJplqq&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571818&hash=d4002befd706c4a18eafc28bb538940bf9cad62d1829a327e5b0c96f62a620a3',
  page: '/bingo',
  stake: 0,
  gameId: '66ca85a744aef7a0c6571b9b'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{ bingo: true, gameId: '66ca85a744aef7a0c6571b9b', selectedNumber: 46 }
[
  32, 59,  75, 29, 70, 3,  50, 47, 25, 67, 49, 28,
  12, 42,  27, 46, 14, 36, 62, 52, 58, 74, 18, 31,
  26, 54,  44, 68, 66, 53, 5,  56, 2,  23, 73, 38,
  16, 61,  9,  72, 39, 41, 1,  19, 43, 35, 4,  13,
  40, 30,  17, 20, 34, 63, 33, 22, 64, 60, 48, 51,
  8,  21,  6,  37, 45, 69, 71, 65, 57, 24, 11, 55,
  10, '*'
] [
  [ 23, 18, 41, 47, 38 ],
  [ 73, 3, 15, 57, 13 ],
  [ 11, 63, '*', 22, 47 ],
  [ 49, 69, 55, 45, 65 ],
  [ 38, 33, 57, 59, 34 ]
]
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeJplqq&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571818&hash=d4002befd706c4a18eafc28bb538940bf9cad62d1829a327e5b0c96f62a620a3',
  page: '/choose-board',
  stake: 0
}
{ selected: 78 }
{ selected: 86 }
{ selected: 69 }
{ selected: 98 }
{ selected: 90 }
{ join: '66cae23c44aef7a0c6571c0d' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeJplqq&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571818&hash=d4002befd706c4a18eafc28bb538940bf9cad62d1829a327e5b0c96f62a620a3',
  page: '/bingo',
  stake: 0,
  gameId: '66cae23c44aef7a0c6571c0d'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfvIAKj&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724571714&hash=de136f60b99f6e5a7904559e8c74701f1faf0e8b41e74892c8ec9718152f0978',
  page: '/choose-board',
  stake: 10
}
error: [polling_error] {"code":"EFATAL","message":"EFATAL: Error: socket hang up"}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2Ovf4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724575273&hash=06b41aaee3869e892e57c1ac1ce5b162d7436f0769fe3f8087b05b7efffda6f3',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBd4vbc4&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724619646&hash=1fe4bceca767eb4c566ef7e8f410513bb140059d8c89a441f50e7de57b69de1f',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcYce5y&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724619662&hash=3348cb6aebb40b842bfc62312e21b45e82be61bbfd5db72cfc7726836d137c9c',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBccSH9x&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620479&hash=5ed0f58801f2642418a15f59a8ebb5fe8ea116818495f0c84b50993f0b440928',
  page: '/choose-board',
  stake: 0
}
{ selected: 26 }
{ join: '66cae23c44aef7a0c6571c0d' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBccSH9x&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620479&hash=5ed0f58801f2642418a15f59a8ebb5fe8ea116818495f0c84b50993f0b440928',
  page: '/bingo',
  stake: 0,
  gameId: '66cae23c44aef7a0c6571c0d'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfgs51o&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620810&hash=d44c5cd5b347458b7cc996e369a4e28e7ccbee69ba8e44df034b0f3186e75ba1',
  page: '/choose-board',
  stake: 0
}
{ selected: 16 }
{ join: '66cae23c44aef7a0c6571c0d' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfgs51o&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620810&hash=d44c5cd5b347458b7cc996e369a4e28e7ccbee69ba8e44df034b0f3186e75ba1',
  page: '/bingo',
  stake: 0,
  gameId: '66cae23c44aef7a0c6571c0d'
}
{ bingo: true, gameId: '66cae23c44aef7a0c6571c0d', selectedNumber: 90 }
TypeError: Cannot read properties of undefined (reading 'push')
    at checkWin (/home/<USER>/bingobot/server.js:880:10)
    at WebSocket.<anonymous> (/home/<USER>/bingobot/server.js:700:17)
    at runMicrotasks (<anonymous>)
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfgs51o&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620810&hash=d44c5cd5b347458b7cc996e369a4e28e7ccbee69ba8e44df034b0f3186e75ba1',
  page: '/bingo',
  stake: 0,
  gameId: '66cae23c44aef7a0c6571c0d'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfgs51o&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620810&hash=d44c5cd5b347458b7cc996e369a4e28e7ccbee69ba8e44df034b0f3186e75ba1',
  page: '/bingo',
  stake: 0,
  gameId: '66cae23c44aef7a0c6571c0d'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfgs51o&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620810&hash=d44c5cd5b347458b7cc996e369a4e28e7ccbee69ba8e44df034b0f3186e75ba1',
  page: '/choose-board',
  stake: 0
}
{ selected: 18 }
{ selected: 19 }
{ join: '66cae23c44aef7a0c6571c0d' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfgs51o&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724620810&hash=d44c5cd5b347458b7cc996e369a4e28e7ccbee69ba8e44df034b0f3186e75ba1',
  page: '/bingo',
  stake: 0,
  gameId: '66cae23c44aef7a0c6571c0d'
}
Express server is listening on 443
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSFHOLql&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724652965&hash=bb4de4f378905f55d4ed7f0a5f8877e98329cfedcd4154dadffe74daea1c9b1b',
  page: '/choose-board',
  stake: 0
}
node:events:491
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::443
    at Server.setupListenHandle [as _listen2] (node:net:1463:16)
    at listenInCluster (node:net:1511:12)
    at Server.listen (node:net:1599:7)
    at Object.<anonymous> (/home/<USER>/bingobot/server.js:126:9)
    at Module._compile (node:internal/modules/cjs/loader:1198:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1252:10)
    at Module.load (node:internal/modules/cjs/loader:1076:32)
    at Function.Module._load (node:internal/modules/cjs/loader:911:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:81:12)
    at node:internal/main/run_main_module:22:47
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1490:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 443
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqkBUV&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653083&hash=12afeb9f978119fd569a462eb94571a945e4a314b219f665f64b17925632b07d',
  page: '/choose-board',
  stake: 0
}
node:events:491
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::443
    at Server.setupListenHandle [as _listen2] (node:net:1463:16)
    at listenInCluster (node:net:1511:12)
    at Server.listen (node:net:1599:7)
    at Object.<anonymous> (/home/<USER>/bingobot/server.js:126:9)
    at Module._compile (node:internal/modules/cjs/loader:1198:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1252:10)
    at Module.load (node:internal/modules/cjs/loader:1076:32)
    at Function.Module._load (node:internal/modules/cjs/loader:911:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:81:12)
    at node:internal/main/run_main_module:22:47
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1490:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 443
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqkBUV&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653083&hash=12afeb9f978119fd569a462eb94571a945e4a314b219f665f64b17925632b07d',
  page: '/choose-board',
  stake: 0
}
node:events:491
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::443
    at Server.setupListenHandle [as _listen2] (node:net:1463:16)
    at listenInCluster (node:net:1511:12)
    at Server.listen (node:net:1599:7)
    at Object.<anonymous> (/home/<USER>/bingobot/server.js:126:9)
    at Module._compile (node:internal/modules/cjs/loader:1198:14)
    at Object.Module._extensions..js (node:internal/modules/cjs/loader:1252:10)
    at Module.load (node:internal/modules/cjs/loader:1076:32)
    at Function.Module._load (node:internal/modules/cjs/loader:911:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:81:12)
    at node:internal/main/run_main_module:22:47
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1490:8)
    at processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 443
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqkBUV&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653083&hash=12afeb9f978119fd569a462eb94571a945e4a314b219f665f64b17925632b07d',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqkBUV&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653083&hash=12afeb9f978119fd569a462eb94571a945e4a314b219f665f64b17925632b07d',
  page: '/choose-board',
  stake: 0
}
{ join: '66cc1d796d0bc7e5dd494fed' }
{ selected: 76 }
{ join: '66cc1d796d0bc7e5dd494fed' }
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqkBUV&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653083&hash=12afeb9f978119fd569a462eb94571a945e4a314b219f665f64b17925632b07d',
  page: '/bingo',
  stake: 0,
  gameId: '66cc1d796d0bc7e5dd494fed'
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqkBUV&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653083&hash=12afeb9f978119fd569a462eb94571a945e4a314b219f665f64b17925632b07d',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSGqE0Rd&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724653841&hash=315140f69a66884ec1479da2132c343aaab57e393e38ee21018c5d7cde09e4f5',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2hyp0&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659522&hash=ecb29479d6dec26a153bddf2d58f9b6e3c9e6757ef5bc74641983c08ecaaa9ee',
  page: '/choose-board',
  stake: 0
}
{ selected: 89 }
{ join: '66cc1d796d0bc7e5dd494fed' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBf2hyp0&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659522&hash=ecb29479d6dec26a153bddf2d58f9b6e3c9e6757ef5bc74641983c08ecaaa9ee',
  page: '/bingo',
  stake: 0,
  gameId: '66cc1d796d0bc7e5dd494fed'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcf8EF5&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659788&hash=9f2b5e0aeeaeb96bd8ab200e1890e87711747e98945f8b3e04d4e2455c9fbb46',
  page: '/choose-board',
  stake: 0
}
{ selected: 100 }
{ selected: 97 }
{ selected: 87 }
{ selected: 78 }
{ selected: 69 }
{ selected: 60 }
{ selected: 78 }
{ selected: 70 }
{ selected: 60 }
{ selected: 70 }
{ selected: 78 }
{ selected: 69 }
{ join: '66cc1d796d0bc7e5dd494fed' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcf8EF5&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659788&hash=9f2b5e0aeeaeb96bd8ab200e1890e87711747e98945f8b3e04d4e2455c9fbb46',
  page: '/choose-board',
  stake: 0
}
{ selected: 56 }
{ selected: 49 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{ selected: 77 }
{ selected: 89 }
{ selected: 100 }
{ selected: 77 }
{ selected: 70 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{ selected: 85 }
{ selected: 61 }
{ selected: 70 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcf8EF5&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659788&hash=9f2b5e0aeeaeb96bd8ab200e1890e87711747e98945f8b3e04d4e2455c9fbb46',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfcJI7n&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724659502&hash=a89b689986d6f58a4930aef1ed84e27464f275bd21b8f36f5887f80473fede05',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBe9U9Zz&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724663805&hash=ed13f69a7cea9ec529bcd10d81f48d8d438edc8f633d21b480e6c96fd5b8e095',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeMpwoA&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724663808&hash=a8ddacefa5235ddaaad267dd38e3a0b6136215458862ecf20a743d672adbce4a',
  page: '/choose-board',
  stake: 0
}
{ selected: 27 }
{ selected: 100 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeMpwoA&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724663808&hash=a8ddacefa5235ddaaad267dd38e3a0b6136215458862ecf20a743d672adbce4a',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfD8jVl&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724671501&hash=035d80071f701f67a8888cf0144b5cd0bfd48cef3b87d56196885c49e2a2ddf4',
  page: '/choose-board',
  stake: 0
}
{ join: '66cc38dd6d0bc7e5dd495029' }
{ selected: 89 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfD8jVl&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724671501&hash=035d80071f701f67a8888cf0144b5cd0bfd48cef3b87d56196885c49e2a2ddf4',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdVmHwn&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724671516&hash=4df60fb91b88b9495f0a5165ce61f0b702989138b03ad16a5f5fc8e37bb86237',
  page: '/choose-board',
  stake: 10
}
{ join: '66cc1d796d0bc7e5dd494fee' }
{ selected: 46 }
{ join: '66cc1d796d0bc7e5dd494fee' }
{ join: '66cc1d796d0bc7e5dd494fee' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeX4ImX&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724671528&hash=c72510f5f536df97470cfb9864a87582f96e4201ff9e0539d0e61a35eacd93d5',
  page: '/choose-board',
  stake: 0
}
{ selected: 36 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeX4ImX&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724671528&hash=c72510f5f536df97470cfb9864a87582f96e4201ff9e0539d0e61a35eacd93d5',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
error: [polling_error] {"code":"EFATAL","message":"EFATAL: Error: read ECONNRESET"}
{
  auth: 'query_id=AAGp5s0hAAAAAKnmzSEUGirF&user=%7B%22id%22%3A567142057%2C%22first_name%22%3A%22Rash%C4%ABd%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22reubentilahun%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724686661&hash=4e55202f31ec1457bdda24db2d4e254526aa8b30f1b5e494050977eb1ee798f6',
  page: '/choose-board',
  stake: 0
}
(node:312147) [node-telegram-bot-api] DeprecationWarning: In the future, content-type of files you send will default to "application/octet-stream". See https://github.com/yagop/node-telegram-bot-api/blob/master/doc/usage.md#sending-files for more information on how sending files has been improved and on how to disable this deprecation message altogether.
(Use `node --trace-deprecation ...` to show where the warning was created)
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBe5-lCm&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724689449&hash=f56d6237659b86999596ae8dbb3f60e71d163bb19456bb7432240e2ac3ed90fa',
  page: '/choose-board',
  stake: 0
}
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBe5-lCm&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724689449&hash=f56d6237659b86999596ae8dbb3f60e71d163bb19456bb7432240e2ac3ed90fa',
  page: '/choose-board',
  stake: 10
}
{ selected: 67 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBd1_MVF&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724689471&hash=669cd5cacca4dec4e4e21aaa4531b1fbb26ea83efe8369b8137e3ecbd337e4c3',
  page: '/choose-board',
  stake: 10
}
{ selected: 47 }
{ selected: 77 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdqEma-&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724689519&hash=aba3f7f69ae97cb1108f59bc72799403894f85a7b112890f8de5338588be8fb6',
  page: '/choose-board',
  stake: 50
}
{ selected: 68 }
{ join: '66cc1d796d0bc7e5dd494ff0' }
{ selected: 100 }
{ join: '66cc1d796d0bc7e5dd494ff0' }
{ selected: 69 }
{ selected: 57 }
{ selected: 50 }
{ selected: 66 }
{ selected: 37 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfENZ2N&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724694069&hash=500167449110691451d4edda67b6f79b16184af73d4220dc94317708a1bc4b79',
  page: '/choose-board',
  stake: 0
}
{ selected: 90 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfENZ2N&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724694069&hash=500167449110691451d4edda67b6f79b16184af73d4220dc94317708a1bc4b79',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeCDXyy&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724694240&hash=7b42100c9215cf2c8697716342f05b752ac89a4be529ff9644a69d0ffa3fbc3c',
  page: '/choose-board',
  stake: 0
}
{ join: '66cc38dd6d0bc7e5dd495029' }
{ selected: 56 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBeCDXyy&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724694240&hash=7b42100c9215cf2c8697716342f05b752ac89a4be529ff9644a69d0ffa3fbc3c',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbidzl1vl&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724695191&hash=c6b009334168a182159acd3ca475e766e10e8e53446def3a28e6bedf99816b9b',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBd68V7Y&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700217&hash=c034a6a960d0cf53ffdb4e0d7b08b8e90fde940f987f32e571d546271b0fc9f6',
  page: '/choose-board',
  stake: 10
}
{ selected: 90 }
{ selected: 89 }
{ join: '66cc1d796d0bc7e5dd494fee' }
{ join: '66cc1d796d0bc7e5dd494fee' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBdJp5h3&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700522&hash=5ec2e9a9e08b0389644871a81f89934758535471f4f0d84c6289aa758931e4a7',
  page: '/choose-board',
  stake: 10
}
{ join: '66cc1d796d0bc7e5dd494fee' }
{ selected: 58 }
{ join: '66cc1d796d0bc7e5dd494fee' }
{ join: '66cc1d796d0bc7e5dd494fee' }
{ join: '66cc1d796d0bc7e5dd494fee' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfHTgdD&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700589&hash=dbcf3de52df099cd71e7676248c83bbc9e52f5d230739f2887107247853e0440',
  page: '/choose-board',
  stake: 0
}
{ selected: 80 }
{ selected: 71 }
{ selected: 69 }
{ selected: 52 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfHTgdD&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700589&hash=dbcf3de52df099cd71e7676248c83bbc9e52f5d230739f2887107247853e0440',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfHTgdD&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700589&hash=dbcf3de52df099cd71e7676248c83bbc9e52f5d230739f2887107247853e0440',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBfNNcIx&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700688&hash=e2f865ed9f52f997ac20b4dd0a8825e1dfe48bebe1d61a0d50fb485e070b480c',
  page: '/choose-board',
  stake: 0
}
{ selected: 79 }
{ selected: 80 }
{ selected: 100 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcIIcp1&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700720&hash=c4aae42cb134a42efb0f50ca13be396fe17307ea95a676b8ecdf9c0b6a10e4f8',
  page: '/choose-board',
  stake: 0
}
{ selected: 100 }
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbifR_eiM&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700762&hash=5111239ee1481dbdd362c7aa8b7a4f1336cad13bcad02d10aeebebf3c1baf27f',
  page: '/bingo',
  stake: 0,
  gameId: 0
}
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbicnnB2I&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700789&hash=ec2b1f7ef47107f0681d638e24ddfe07f0d6c9ec5d799f258666483810fb586c',
  page: '/choose-board',
  stake: 0
}
{ join: '66cc38dd6d0bc7e5dd495029' }
{ selected: 43 }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcIIcp1&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700720&hash=c4aae42cb134a42efb0f50ca13be396fe17307ea95a676b8ecdf9c0b6a10e4f8',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbicnnB2I&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700789&hash=ec2b1f7ef47107f0681d638e24ddfe07f0d6c9ec5d799f258666483810fb586c',
  page: '/choose-board',
  stake: 0
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcIIcp1&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700720&hash=c4aae42cb134a42efb0f50ca13be396fe17307ea95a676b8ecdf9c0b6a10e4f8',
  page: '/choose-board',
  stake: 0
}
{ selected: 21 }
{ selected: 100 }
{ selected: 34 }
{ selected: 80 }
{ join: '66cc38dd6d0bc7e5dd495029' }
{ join: '66cc38dd6d0bc7e5dd495029' }
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcIIcp1&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700720&hash=c4aae42cb134a42efb0f50ca13be396fe17307ea95a676b8ecdf9c0b6a10e4f8',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbicnnB2I&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700789&hash=ec2b1f7ef47107f0681d638e24ddfe07f0d6c9ec5d799f258666483810fb586c',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbicnnB2I&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700789&hash=ec2b1f7ef47107f0681d638e24ddfe07f0d6c9ec5d799f258666483810fb586c',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  bingo: true,
  gameId: '66cc38dd6d0bc7e5dd495029',
  selectedNumber: 100
}
[
  34, 54, 19, 22, 46,  73, 65, 37, 53, 6,  16, 67,
  4,  62, 8,  14, 20,  56, 68, 40, 44, 21, 1,  10,
  13, 74, 7,  63, 26,  66, 59, 25, 27, 42, 51, 12,
  75, 35, 50, 33, 60,  3,  31, 28, 69, 24, 43, 58,
  47, 39, 18, 2,  '*'
] [
  [ 5, 14, 36, 19, 20 ],
  [ 59, 35, 6, 27, 10 ],
  [ 43, 3, '*', 37, 63 ],
  [ 28, 25, 56, 10, 1 ],
  [ 46, 16, 44, 65, 13 ]
]
{
  auth: 'query_id=AAFEHG4nAwAAAEQcbicnnB2I&user=%7B%22id%22%3A7103978564%2C%22first_name%22%3A%22Dynamic%20Residences%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22Dynamic_Residences%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700789&hash=ec2b1f7ef47107f0681d638e24ddfe07f0d6c9ec5d799f258666483810fb586c',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
{
  auth: 'query_id=AAGsb8AXAAAAAKxvwBcIIcp1&user=%7B%22id%22%3A398487468%2C%22first_name%22%3A%22YISHAK%20BIRHANU%22%2C%22last_name%22%3A%22%22%2C%22username%22%3A%22yishak_birhanu%22%2C%22language_code%22%3A%22en%22%2C%22allows_write_to_pm%22%3Atrue%7D&auth_date=1724700720&hash=c4aae42cb134a42efb0f50ca13be396fe17307ea95a676b8ecdf9c0b6a10e4f8',
  page: '/bingo',
  stake: 0,
  gameId: '66cc38dd6d0bc7e5dd495029'
}
