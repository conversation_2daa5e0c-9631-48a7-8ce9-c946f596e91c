// Game timing constants (currently unused but kept for future features)
const peakMin = 10;
const peakMax = 18;
const lowMin = 1;
const lowMax = 5;

const RECEIVE_BONUS_WHEN = "register_number"

const RAND_DRAW_SEED = 'RAND_DRAW_SEED'
const NUMBER_OF_WINNING_NUMBERS_TO_SKIP = 0

let BACKGROUND_IMAGE = undefined;

let profitSinceBotStarted = 0
let depositsSinceBotStarted = 0;
let withdrawalsSinceBotStarted = 0;

const stakes = {
  0:0, 10: 10, 20: 20, 50: 50, 100: 100
}

const densityMatrix = [
  [3, 2, 1, 1, 1, 2, 3], // 0000 to 0300 - midnight 
  [0, 0, 0, 0, 0, 0, 0], // 0300 to 0600 - midnight to morning
  [10, 12, 12, 13, 14, 15, 11], // 0600 to 9000 - morning 
  [20, 21, 22, 21, 23, 24, 22], // 09:00 to 12:00 - morning to noon
  [45, 44, 43, 46, 47, 45, 48], // 12:00 to 15:00 - noon
  [53, 52, 57, 58, 51, 53, 55], // 15:00 to 18:00 - afternoon
  [36, 37, 35, 36, 38, 37, 40], // 18:00 to 21:00 - night
  [10, 13, 22, 21, 23, 24, 22], // 21:00 to 00:00 - deep night
];

// default values
let SETTINGS = {
  botWinPercentage: {
    10: 0.1,
    20: 0.75,
    50: 0.95
  },
  densityMatrixMultipliers: {
    10: 1,
    20: 0.4,
    50: 0.2
  },
  minimumDensity: 3
}

const saveSettings = () => {
  try {
    fs.writeFileSync('settings.json', JSON.stringify(SETTINGS, null, 2))
    console.log('Settings saved successfully')
  } catch (error) {
    console.error('Failed to save settings:', error)
  }
}

const loadSettings = () => {
  try {
    if (fs.existsSync('settings.json')) {
      SETTINGS = JSON.parse(fs.readFileSync('settings.json', 'utf8'))
      console.log('Settings loaded successfully')
    } else {
      console.log('Settings file not found, using defaults')
      saveSettings() // Create the file with default settings
    }
  } catch (error) {
    console.error('Failed to load settings:', error)
  }
}

const ADMINS = [408231241, 567142057, 398487468]

const gen = require('random-seed');
require('dotenv').config();
require('dotenv').config({ path: `.env.local`, override: true });

const axios = require('axios');
const FormData = require('form-data');
const TelegramBot = require('node-telegram-bot-api');
const express = require('express');
const cors = require('cors')
const {engine} = require('express-handlebars');
const path = require('path');
const mongoose = require('mongoose');
const crypto = require('crypto');
// AutoIncrement plugin - will be properly configured after connection
let AutoIncrement;

const TOKEN = process.env.TELEGRAM_TOKEN;
const port = process.env.PORT || 80;

const prod = "bluebingoet.com"

const socketUrl = `${process.env.DEV ? 'ws' : 'wss' }://${process.env.DEV ? 'localhost:' + (process.env.PORT || 80) : prod}/ws`
const url = `https://${prod}`;

// MongoDB connection with proper error handling and optimization
mongoose.connect(`mongodb+srv://${process.env.MONGO_USER}:${process.env.MONGO_PASSWORD}@cluster0.z133x.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0`, {
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  bufferCommands: false, // Disable mongoose buffering
  bufferMaxEntries: 0 // Disable mongoose buffering
})
  .then(() => {
    console.log('Connected to MongoDB successfully');
    // Initialize AutoIncrement after successful connection
    AutoIncrement = require('mongoose-sequence')(mongoose);
  })
  .catch((error) => {
    console.error('MongoDB connection error:', error);
    process.exit(1); // Exit if database connection fails
  });

const REFERRAL_PERCENTAGE = 0
const STARTING_BALANCE = 0

// Note: 'request' library is deprecated, using axios instead

// Define the User schema with indexes for performance
const userSchema = new mongoose.Schema({
  id: { type: Number, required: true, unique: true, index: true },
  name: { type: String, required: true},
  phone: { type: String, index: true },
  deposit_phone: { type: String }, // transient (for deposits)
  wonFirstGame: { type: Boolean, default: false},
  banned: { type: Boolean, default: false, index: true },
  referral: { type: Number, index: true },
  paidReferral: { type: Boolean, default: false },
  deposit_user: { type: mongoose.Schema.Types.ObjectId },
  transfer_user: { type: mongoose.Schema.Types.ObjectId }, // transient (for transfers)
  withdrawal: {type: [String], required: false, default: undefined}, // transient (for withdrawals)
  comments: {type: [String], required: true, default: []}, // messages sent to support bot (find a way handle them in the future, maybe custom chat interface if it gets big)
  lastSupportMessage: {type: String},
  balance: { type: Number, required: true, default: 0, index: true },
  bonusBalance: { type: Number, required: true, default: 0, index: true },
  games: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Game', required: true, default: [] }], // unused (might populatewith finished games for easier lookups)
  transactions: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Transaction', required: true, default: [] }], // same
  expecting: { type: String }, // transient variable for conversations with bot
}, {
  timestamps: true // Add createdAt and updatedAt automatically
});

// Define the Game schema with indexes for performance
const gameSchema = new mongoose.Schema({
  players: { type: [mongoose.Schema.Types.ObjectId], ref: 'User', required: true, default: [] },
  banned: { type: [mongoose.Schema.Types.ObjectId], ref: 'User', required: true, default: [] },
  numbers: { type: [Number], ref: 'User', required: true, default: [] }, // called  numbers (by players)
  calledNumbers: { type: [String], ref: 'User', required: true, default: [] }, // called  numbers (by draw)
  winner: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true },
  createdAt: { type: Date, default: Date.now, index: true },
  number: {type: Number},
  stake: {type: Number, index: true},
  started: {type: Boolean, default: false, index: true},
  botWon: {type: Boolean, default: false, required: true, index: true},
}, {
  timestamps: true // Add createdAt and updatedAt automatically
});


const transactionSchema = new mongoose.Schema({
  from: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }, // only from is defined if it is deposit
  to: { type: mongoose.Schema.Types.ObjectId, ref: 'User', index: true }, // only to is defined if withdrawal
  amount: { type:Number, required: true, index: true }, // if both are defined it is transfer between players
  complete: {type: Boolean, required: true, default: false, index: true}, // for pending or unverified transactions, (must make a system in the future to audit this stuff, probably event sourcing with queues and snapshots)
  failed: {type: Boolean, default: false, index: true}
}, {
  timestamps: true // Add createdAt and updatedAt automatically
});

function asId(i) {
  try {
    if (!i) return null
    return mongoose.Types.ObjectId.isValid(i) ? new mongoose.Types.ObjectId(i) : null
  } catch (error) {
    console.error('Invalid ObjectId:', i, error)
    return null
  }
}

// Create the User and Game models
const User = mongoose.model('User', userSchema);
const Transaction = mongoose.model('Transaction', transactionSchema);
const Game = mongoose.model('Game', gameSchema);

// AutoIncrement plugin will be applied after MongoDB connection is established
// This will be handled in the connection success callback

// geerates persistent game and in-memory version
async function spinUpGame(g) {
  g = {
    id: g._id.toString(),
    countdown: COUNTDOWN,
    number: g.number, // doesn't work
    stake: g.stake,
    cartela: newCartela(g),
    started: false,
    players: [],
    broadcast: [],
    selected: [],
    botNumbers: [],
    joinedBotNumbers: [],
    botsCalledBingos: [],
    waitingForLobby: [],
    pingOnTelegram: [],
    realMoney: 0
  }
  g.b = (x, id) => g.broadcast.forEach(s => {
    if (s.authenticated != id) s.send(JSON.stringify(x))
  })
  extantGames.push(g)
  return g
}
async function newGame(stake) {
  var g = new Game({stake: stake});
  await g.save()
  
  return await spinUpGame(g)
}
async function respawnOpenGames() {
  try {
    var openGames = await Game.find({ started: false, botWon: false, players: { $ne: [] } });
    openGames = openGames.filter(g => extantGames.findIndex(_g => _g.id == g._id.toString()) == -1)
    openGames.forEach(spinUpGame)
    return openGames.length
  } catch (e) { logError(e) }
}


const fs = require('fs');
const https = require('https');
const privateKey = fs.readFileSync('cert/private.key', 'utf8');
const certificate = fs.readFileSync('cert/certificate.crt', 'utf8');

const botUsernames = JSON.parse(fs.readFileSync('fake_names.txt', 'utf8'))

const app = express();
app.use(cors())

const mockFunc = async (...args) => {console.log(`Fake bot shit`)}
const mockBot = ['setWebhook', 'processUpdate', 'on', 'onText', 'sendMessage', 'sendPhoto', 'answerCallbackQuery'].reduce((a, b) => ({...a, [b]: mockFunc}), {})

const bot = process.env.KILL_TELEGRAM_BOTS ? mockBot : new TelegramBot(TOKEN, { polling: !!process.env.DEV});
const supportBot = process.env.KILL_TELEGRAM_BOTS ? mockBot : new TelegramBot(process.env.SUPPORT_TELEGRAM_TOKEN, { polling: !!process.env.DEV});

// webhooks if on server
if (!process.env.DEV) {
  bot.setWebHook(`${url}/bot${TOKEN}`);
  supportBot.setWebHook(`${url}/bot${process.env.SUPPORT_TELEGRAM_TOKEN}`);
}

// basic templating
app.use(express.static('static'))
app.use(express.json({ extended: false }));
app.engine('handlebars', engine());
app.set('view engine', 'handlebars');
app.set('views', path.join(__dirname, 'views'));

app.post(`/bot${TOKEN}`, (req, res) => {
  try {
    bot.processUpdate(req.body);
    res.sendStatus(200);
  } catch (e) { logError(e) }
});
app.post(`/bot${process.env.SUPPORT_TELEGRAM_TOKEN}`, (req, res) => {
  try {
    supportBot.processUpdate(req.body);
    res.sendStatus(200);
  } catch (e) { logError(e) }
});

const server = https.createServer({
  key: privateKey,
  cert: certificate,
  hostname: prod
}, app).listen(port, () => {
  console.log(`Express server is listening on ${port}`);
});;


// Define a route that renders the template
app.get('/bingo', async (req, res) => {
  var g = await Game.findById(asId(req.query.gameId))
  const data = {
    stake: stakes[req.query.stake] ?? 0,
    derash: 0,
    numPlayers: 0,
    gameId: JSON.stringify(req.query.gameId) || 0,
    socketUrl: JSON.stringify(socketUrl),
    gameNumber: g ? g._id.toString().slice(-6) : '000000', // Use last 6 chars of ObjectId
    cssFile:'/game-page.css?v=8'
  };
  if (g) {
    var inProgressGame = extantGames.find((g) => g.id == req.query.gameId)
    if (inProgressGame) {
      data.numPlayers = getAllJoinedNumbers(inProgressGame).length
      data.derash = calculateDerash(getAllJoinedNumbers(inProgressGame).length, g.stake)
    } else {
      data.numPlayers = g.players.length+getBotDensity(g)
      data.derash = calculateDerash(g.players.length+getBotDensity(g), g.stake)
    }
    data.stake = g.stake
    data.gameNumber = g._id.toString().slice(-6) // Use last 6 chars of ObjectId as game number
  }
  res.render('bingo', data);
});
// Define a route that renders the template
app.get('/choose-board', (req, res) => {
  const data = {
    stake: stakes[req.query.stake] ?? 0,
    socketUrl: JSON.stringify(socketUrl),
    cartela: JSON.stringify([]),
    cssFile:'/choose-board.css?v=4'
  };
  res.render('choose-board', data);
});
// Define a route that renders the template
app.get('/instructions', (_, res) => {
  res.render('instructions', {});
});

app.get('/', (_, res) => {
  res.render('instructions', {});
});

app.get('/ping', (_, res) => {
  res.render('ping', {});
});


app.post("/chapa-approval", async (req, res) => {
  var transaction = await Transaction.findById(asId(req.body.reference))
  if (transaction) res.sendStatus(200)
  else res.sendStatus(400);
})

// handles deposits
app.post("/chapa", async (req, res) => {
  const chapaSig = req.get('Chapa-Signature');
  const secret = process.env.CHAPA_SECRET_HASH
  const signature = crypto.createHmac('sha256', secret).update(secret).digest('hex');

  if (signature !== chapaSig) {
    console.log('Invalid Chapa-Signature');
    res.sendStatus(401);
  } else {
    const transaction = await Transaction.findById(asId(req.body.tx_ref))
    if (transaction && transaction.from && !transaction.to && !transaction.complete) {
      let user = await User.findById(transaction.from) 
      if (user) {
        if (req.body.status == "success") {
          transaction.complete = true;
          user.balance += transaction.amount;
          depositsSinceBotStarted += transaction.amount;
          await transaction.save()
          await user.save()

          await alertAdmins(`${user.name} (${user.id}) deposited ${transaction.amount}ETB.`)
          await safeSendMessage(user.id, 'You have successfully topped up your wallet.\n Please Click /balance to check your account balance.');
          res.sendStatus(200);
        }
      } else res.sendStatus(401);
    } else res.sendStatus(401);
  }
});

let LOCKED_USERS = []

bot.on('callback_query', async (query) => {
  const chatId = query.message.chat.id;
  const data = query.data;

  try {
    var u = await User.findOne({ id: chatId });
    if (!u || u.banned || await checkPhone(chatId, u)) return;

    if (data === 'invite') {
      invite(query.message)
    } else if (data === 'play' || data == 'playbutton') {
      // Send a button that opens the mini-app webview
      play(query.message)
    } else if (data === 'balance') {
      balance(query.message)  
    } else if (data === 'deposit') {
      deposit(query.message)  
    } else if (data === 'register') {
      contact(query.message)  
    } else if (data.split('_')[0] == 'verify') {
      try {
        const response = await axios.get(`https://api.chapa.co/v1/transaction/verify/${data.split('_')[1]}`, {
          headers: {
            'Authorization': `Bearer ${process.env.CHAPA_PRIVATE_KEY}`
          }
        });

        if (!response.data || response.data.status !== "success" || response.data.data.status !== "success") {
          await safeSendMessage(chatId, 'Transaction has not been verified')
        } else {
          var transaction = await Transaction.findById(asId(data.split('_')[1]))
          if (!transaction) await safeSendMessage(chatId, 'Transaction doesn\'t exist.');
          else if (transaction.complete) await safeSendMessage(chatId, 'Transaction has been completed.');
          else if (transaction.from && !transaction.to && !transaction.complete) { // assuming deposit
            var user = await User.findById(transaction.from);
            if (!user) await safeSendMessage(chatId, 'User doesn\'t exist.');
            user.balance += transaction.amount;
            transaction.complete = true;
            await user.save();
            await transaction.save();

            await safeSendMessage(chatId, `Transaction has been completed. ${transaction.amount}ETB has been added to your account.`);
          }
        }
      } catch (error) {
        console.error('Transaction verification error:', error);
        await safeSendMessage(chatId, 'Transaction verification failed. Please try again.');
      }
    } else if (data.split('_')[0] == 'bank') {
      var bank = data.split('_')[1]
      var bankName = data.split('_')[2]
      let user = await User.findOne({ id: chatId });
      if (user && user.withdrawal && user.withdrawal.length == 1) {
        user.withdrawal.push(bank)
        await user.save(); 
        await safeSendMessage(chatId, `Please send the account holder's name for ${getBankName(bankName)}.`)
      }
    } else if (data == 'cancel_withdrawal') {
      let user = await User.findOne({ id: chatId });
      if (user && user.withdrawal) {
        user.withdrawal = undefined
        await user.save()
        await safeSendMessage(chatId, "Withrawal cancelled.")
      }
    } else if (data == 'confirm_withdrawal') {
      let user = await User.findOne({ id: chatId });
      if (user && user.withdrawal && user.withdrawal.length == 4) {
        var [amount, bank, name, number] = user.withdrawal;
        bank = parseInt(bank);
        amount = parseFloat(amount)
        if (user.balance < amount || !amount || amount < 0) {
          await safeSendMessage(chatId, "❌ Insufficient funds.")
        } else {
          var transaction = new Transaction({to: user, amount, complete: false})
          await transaction.save();

          await finalizeTransaction(amount, bank, name, number, user, transaction._id, async () => {
            user.withdrawal = undefined
            user.balance -= amount
            withdrawalsSinceBotStarted += amount
            transaction.complete = true
            await user.save()
            await transaction.save();
            await alertAdmins(`${user.name} (${user.id}) withdrew ${amount}ETB.`)
            await safeSendMessage(
              chatId,
              `✅ <b>Withdrawal Successful!</b>\n\n🏦 <b>Bank:</b> ${getBankName(bank)}\n👤 <b>Account Name:</b> ${name}\n🔢 <b>Account Number:</b> ${number}\n💵 <b>Amount:</b> ${amount.toFixed(2)} ETB\n🔗 <b>Reference:</b> ${transaction._id}\n\n💰 Your funds will be processed within 24 hours.`,
              {parse_mode: 'html'}
            )
          }, async (error) => {
            await alertAdmins(`${user.name} (${user.id}) failed to withdrew ${amount}ETB. \n\n${error}`)
            await safeSendMessage(chatId, "❌ Withdrawal failed. Please try again or contact support.")
          })
        }
      }
    } else if (data == 'cancel_deposit') {
      let user = await User.findOne({ id: chatId });
      if (user) {
        user.expecting = undefined
        user.deposit_phone = undefined
        await user.save()
        await safeSendMessage(chatId, "❌ Deposit cancelled.")
      }
    } else if (data == 'withdraw') {
      // Handle withdraw button click
      const user = await User.findOne({ id: chatId });
      if (!user || await checkPhone(chatId, user)) return;

      if (user.balance < 100) {
        await safeSendMessage(chatId, '❌ Minimum withdrawal amount is 100 ETB. Your current balance is insufficient.')
      } else {
        user.withdrawal = []
        await safeSendMessage(chatId, '💰 <b>Withdraw Funds</b>\n\n💵 Please enter the amount you want to withdraw.\n\n⚠️ <i>Minimum withdrawal: 100 ETB</i>')
        await user.save()
      }
    } else if (data == 'copy_referral_link') {
      const referralLink = `https://t.me/bluebingobot?start=${chatId}`;
      await safeSendMessage(chatId, `📋 <b>Referral Link Copied!</b>\n\n<code>${referralLink}</code>\n\n💡 <i>Share this link with your friends to earn 10% of their winnings!</i>`, {
        parse_mode: 'html'
      })
    } else if (data == 'back_to_menu') {
      // Simulate /start command
      await bot.emit('message', {
        chat: { id: chatId },
        from: { first_name: u?.name || 'User' },
        text: '/start'
      })
    }
    await bot.answerCallbackQuery(query.id);
  } catch (e) { logError(e) }
});

const finalizeTransaction = async (amount, bank, name, number, user, reference, successCallback, failureCallback) => {
  try {
    while (LOCKED_USERS.includes(user.id)) await new Promise(r => setTimeout(r, 1000))
    LOCKED_USERS.push(user.id)
    user = await User.findOne({ _id: user._id });
    if (user.balance < amount || !amount || amount < 0) {
      LOCKED_USERS = LOCKED_USERS.filter(x => x != user.id)
      return await safeSendMessage(user.id, "Insufficient funds.")
    }
            

    try {
      const response = await axios.post('https://api.chapa.co/v1/transfers', {
        "account_name": name,
        "account_number": number,
        "amount": ''+amount,
        "currency": "ETB",
        "reference": reference,
        "bank_code": bank
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.CHAPA_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.status !== "success") {
        await failureCallback(`Transaction Failed\n\nResponse: ${JSON.stringify(response.data)}`)
      } else {
        await successCallback()
      }
    } catch (err) {
      await failureCallback(`${err.toString()}\n\nError details: ${err.response ? JSON.stringify(err.response.data) : 'No response data'}`)
    } finally {
      LOCKED_USERS = LOCKED_USERS.filter(x => x != user.id)
    }

  } catch (e) { logError(e) }
}

const validPhone = (phone) => {
  if (!phone) return undefined

  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '')

  // Ethiopian phone number validation
  if (cleanPhone.length === 10 && cleanPhone.startsWith('0')) {
    return cleanPhone // Already in correct format
  }
  if (cleanPhone.length === 9 && !cleanPhone.startsWith('0')) {
    return '0' + cleanPhone // Add leading zero
  }
  if (cleanPhone.length === 12 && cleanPhone.startsWith('251')) {
    return '0' + cleanPhone.substr(3) // Convert from international format
  }
  if (cleanPhone.length === 13 && cleanPhone.startsWith('+251')) {
    return '0' + cleanPhone.substr(4) // Convert from international format with +
  }

  return undefined // Invalid format
}
const invite = async (msg) => {
  const chatId = msg.chat.id;
  const user = await User.findOne({ id: chatId });

  if (!user) {
    return await safeSendMessage(chatId, '❌ Please register first by pressing /start')
  }

  const referralCount = await User.countDocuments({ referral: chatId });
  const referralLink = `https://t.me/bluebingobot?start=${chatId}`;

  await safeSendMessage(chatId, `✉️ <b>Invite Friends & Earn!</b>\n\n🎁 <b>Your Referral Benefits:</b>\n• Get 10% of all your friends' winnings\n• Help your friends get started\n• Build your passive income\n\n👥 <b>Friends Referred:</b> ${referralCount}\n\n🔗 <b>Your Referral Link:</b>\n<code>${referralLink}</code>\n\n📱 <i>Share this link with your friends!</i>`, {
    parse_mode: 'html'
  });

  await safeSendPhoto(chatId, BACKGROUND_IMAGE, {
    caption: `🎮 Join me on Blue Bingo!\n\n🎯 Play exciting Bingo games\n💰 Win real money\n🎁 Get bonus when you join through my link!`,
    reply_markup: {
      inline_keyboard: [
        [{ text: '🎮 Join Blue Bingo', url: referralLink}],
        [{ text: '📋 Copy Link', callback_data: 'copy_referral_link' }]
      ]
    }
  })
}
const instructions = async (msg) => {
  const chatId = msg.chat.id;

  await safeSendPhoto(chatId, BACKGROUND_IMAGE, {
    caption: `Get started with Blue Bingo today!`,
    reply_markup: {
      inline_keyboard: [[{ text: 'Instructions', web_app: {url}}]]
    }
  })
}
const contact = async (msg) => {
  const chatId = msg.chat.id;
  const phone = validPhone(msg.contact?.phone_number || msg.text)

  let user = await User.findOne({ id: chatId });
  if (!user) {
    await safeSendMessage(chatId, "Press /start.");
  } else if (user.phone) {
    await safeSendMessage(
      chatId, 
      `You have been successfully registered!\nClick /play to start the game.`,
      {
        reply_markup: {
          remove_keyboard: true
        }
      }
    );
  } else if (phone) {
    user.phone = phone
    await user.save()
    if (RECEIVE_BONUS_WHEN == "register_number" && !user.paidReferral && user.referral) await addReferralBonus(user)
    await safeSendMessage(
      chatId,
      `🎉 Registration successful!\n\n📱 Phone: ${phone}\n💰 Starting balance: ${user.balance + user.bonusBalance} ETB\n\n🎮 Ready to play? Click /play to start your first game!`,
      {
        reply_markup: {
          remove_keyboard: true,
          inline_keyboard: [
            [{ text: '🎮 Start Playing', callback_data: 'play' }],
            [{ text: '💰 Check Balance', callback_data: 'balance' }]
          ]
        }
      }
    );
  } else {
    await safeSendMessage(
      chatId,
      '📱 Please provide a valid Ethiopian phone number.\n\nAccepted formats:\n• 0912345678 (10 digits)\n• 912345678 (9 digits)\n• +251912345678 (international)\n• 251912345678 (country code)',
      {
        reply_markup: {
          keyboard: [
            [{
              text: '📱 Share Contact',
              request_contact: true
            }]
          ],
          resize_keyboard: true,
          one_time_keyboard: true
        }
      }
    )
  }
}
bot.on('contact', contact)

const askPhone = async (chatId) => {
  await safeSendMessage(
    chatId, 
    `To complete your registration, please click the 'Share Contact' button below to share your phone number. You can also use /change_name to change your name.`,
    {
      reply_markup: {
        keyboard: [
          [{
            text: 'Share Contact',
            request_contact: true
          }]
        ]
      }
    }
  );
}


// Support Bot

// Admin functions
supportBot.onText(/\/stats/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  try {

    // Optimize database queries with aggregation
    const [incomingResult] = await Transaction.aggregate([
      { $match: { complete: true, to: { $exists: false } } },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ])
    const incoming = incomingResult?.total || 0

    const [outgoingResult] = await Transaction.aggregate([
      { $match: { complete: true, from: { $exists: false } } },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ])
    const outgoing = outgoingResult?.total || 0

    const [balanceResult] = await User.aggregate([
      { $match: { balance: { $ne: 0 } } },
      { $group: { _id: null, total: { $sum: "$balance" } } }
    ])
    const balances = balanceResult?.total || 0

    const [bonusBalanceResult] = await User.aggregate([
      { $match: { bonusBalance: { $ne: 0 } } },
      { $group: { _id: null, total: { $sum: "$bonusBalance" } } }
    ])
    const bonusBalances = bonusBalanceResult?.total || 0

    const activePlayers = extantGames.filter(g => g.started).reduce((a, b) => a + b.selected.length, 0)
    const lobbyPlayers = extantGames.filter(g => !g.started).reduce((a, b) => a + b.selected.length, 0)

    const profit = (incoming - outgoing) - balances

    // Get counts in parallel for better performance
    const [userCount, gameCount, transactionCount] = await Promise.all([
      User.countDocuments({}),
      Game.countDocuments({}),
      Transaction.countDocuments({})
    ])

    await supportsafeSendMessage(chatId, `Users: ${userCount}\nGames: ${gameCount}\nTransactions: ${transactionCount}\n\nLobby Players: ${lobbyPlayers}\nActive Players: ${activePlayers}\n\nIncoming: ${incoming} ETB\nOutgoing: ${outgoing} ETB\n\nPlayer Balances: ${balances} ETB\nBonus Balances: ${bonusBalances} ETB\n\nAll-time Profit: ${profit} ETB\nProfit since bot start: ${profitSinceBotStarted}\nDeposits since bot start: ${depositsSinceBotStarted}\nWithdrawals since bot start: ${withdrawalsSinceBotStarted}`)
  } catch (e) { logError(e) }
})

const getChapaStats = async () => {
  try {
    let incoming = 0
    let outgoing = 0
    let transactionsPage = 0
    let transfersPage = 0

    while (transfersPage != -1) {
      await axios.get('https://api.chapa.co/v1/transfers', {
        'params': {
          page: transfersPage+1
        },
        'headers': {
          'Authorization': `Bearer ${process.env.CHAPA_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        }
      }).then(response => {
        outgoing = response.data.data.reduce((acc, a) => acc+parseFloat(a.amount)-parseFloat(a.charge), outgoing)
        transfersPage = response.data.meta.next_page_url ? (transfersPage+1) : -1
        if (process.env.DEV) console.log(`Outgoing: ${outgoing}`)
      });
    }

    while (transactionsPage != -1) {
      await axios.get('https://api.chapa.co/v1/transactions', {
        'params': {
          page: transactionsPage+1
        },
        'headers': {
          'Authorization': `Bearer ${process.env.CHAPA_PRIVATE_KEY}`,
          'Content-Type': 'application/json'
        }
      }).then(response => {
        incoming = response.data.data.transactions.reduce((acc, a) => acc+parseFloat(a.amount)-parseFloat(a.charge), incoming)
        transactionsPage = response.data.data.pagination.next_page_url ? (transactionsPage+1) : -1
        if (process.env.DEV) console.log(`Incoming: ${incoming}`)
      });
    }

    // Use aggregation for better performance
    const [balanceResult] = await User.aggregate([
      { $match: { balance: { $ne: 0 } } },
      { $group: { _id: null, total: { $sum: "$balance" } } }
    ])
    const balances = balanceResult?.total || 0

    const [bonusBalanceResult] = await User.aggregate([
      { $match: { bonusBalance: { $ne: 0 } } },
      { $group: { _id: null, total: { $sum: "$bonusBalance" } } }
    ])
    const bonusBalances = bonusBalanceResult?.total || 0

    const activePlayers = extantGames.filter(g => g.started).reduce((a, b) => a + b.selected.length, 0)
    const lobbyPlayers = extantGames.filter(g => !g.started).reduce((a, b) => a + b.selected.length, 0)

    const profit = (incoming - outgoing) - balances

    return `Incoming: ${incoming} ETB\nOutgoing: ${outgoing} ETB\n\nPlayer Balances: ${balances} ETB\nBonus Balances: ${bonusBalances} ETB\n\nActive Players: ${activePlayers}\nLobby Players: ${lobbyPlayers}\n\nAll-time Profit: ${profit} ETB`
  } catch (e) { logError(e) }
}

supportBot.onText(/\/chapastats/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  try {
    await supportsafeSendMessage(chatId, await getChapaStats())
  } catch (e) { logError(e) }
})

supportBot.onText(/\/settings/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  try {
    await supportsafeSendMessage(chatId, JSON.stringify(SETTINGS, null, 2))
  } catch (e) { logError(e) }
})

supportBot.onText(/\/addbalance/, async (msg) => {
  const chatId = msg.chat.id;
  if (!ADMINS.includes(chatId)) return; // Only allow admins to use this command

  try {
    const args = msg.text.split(' ').slice(1); // Split and remove the command part (/addbalance)
    const targetChatId = parseInt(args[0]); // First argument is the target chat ID
    const amountToAdd = parseFloat(args[1]); // Second argument is the amount to add

    if (!isNaN(targetChatId) && !isNaN(amountToAdd)) {
      // Fetch the user from the database using their chat ID
      let user = await User.findOne({ id: targetChatId });

      if (user) {
        // Add the balance to the user's account
        user.balance = (user.balance || 0) + amountToAdd;
        await user.save(); // Save the updated user object back to the database

        await supportBot.sendMessage(chatId, `Successfully added ${amountToAdd} to user ${targetChatId}'s balance.`);
        await safeSendMessage(targetChatId, `Congratulation! You have recieved ${amountToAdd} ETB to your Main balance. Enjoy your game!`);
      } else {
        // User not found
        await supportBot.sendMessage(chatId, `User with chat ID ${targetChatId} not found.`);
      }
    } else {
      // Invalid format or arguments
      await supportBot.sendMessage(chatId, "Invalid format. Use /addbalance [chatID] [Amount].");
    }
  } catch (e) {
    logError(e); // Log any errors
    await supportBot.sendMessage(chatId, "An error occurred while updating the balance.");
  }
});

supportBot.onText(/\/addbonus/, async (msg) => {
  const chatId = msg.chat.id;
  if (!ADMINS.includes(chatId)) return; // Only allow admins to use this command

  try {
    const args = msg.text.split(' ').slice(1); // Split and remove the command part (/addbalance)
    const targetChatId = parseInt(args[0]); // First argument is the target chat ID
    const amountToAdd = parseFloat(args[1]); // Second argument is the amount to add

    if (!isNaN(targetChatId) && !isNaN(amountToAdd)) {
      // Fetch the user from the database using their chat ID
      let user = await User.findOne({ id: targetChatId });

      if (user) {
        // Add the balance to the user's account
        user.bonusBalance = (user.bonusBalance || 0) + amountToAdd;
        await user.save(); // Save the updated user object back to the database

        await supportBot.sendMessage(chatId, `Successfully added ${amountToAdd} to user ${targetChatId}'s Bonus balance.`);
        await safeSendMessage(targetChatId, `Congratulation! You have recieved ${amountToAdd} ETB to your Bonus balance. Enjoy your game!`);
      } else {
        // User not found
        await supportBot.sendMessage(chatId, `User with chat ID ${targetChatId} not found.`);
      }
    } else {
      // Invalid format or arguments
      await supportBot.sendMessage(chatId, "Invalid format. Use /addbonus [chatID] [Amount].");
    }
  } catch (e) {
    logError(e); // Log any errors
    await supportBot.sendMessage(chatId, "An error occurred while updating the balance.");
  }
});

supportBot.onText(/\/sendmsg/, async (msg) => {
  const chatId = msg.chat.id;
  if (!ADMINS.includes(chatId)) return; // Only allow admins to use this command
  
  try {
    const args = msg.text.split(' ').slice(1); // Split and remove the command part (/sendmsg)
    const targetChatId = parseInt(args[0]); // First argument is the target chat ID
    const messageToSend = args.slice(1).join(' '); // Join the rest as the message
    
    if (!isNaN(targetChatId) && messageToSend) {
      await safeSendMessage(targetChatId, messageToSend); // Send the message to the target chat ID
      await supportsafeSendMessage(chatId, "Message sent successfully."); // Confirm to the admin
    } else {
      await supportsafeSendMessage(chatId, "Invalid format. Use /sendmsg [chatID] [Message].");
    }
  } catch (e) {
    logError(e); // Log any errors
    await supportsafeSendMessage(chatId, "An error occurred while sending the message.");
  }
});

supportBot.onText(/\/setdensity/, async (msg) => {
  const chatId = msg.chat.id;
  if (!ADMINS.includes(chatId)) return
  try {
    const [, stakeStr, densityStr] = msg.text.split(' ')
    const stake = parseInt(stakeStr)
    const density = parseFloat(densityStr)

    if ([10, 20, 50, 100].includes(stake) && !isNaN(density) ) {
      density = Math.min(Math.max(density, 0),1)
      
      SETTINGS.densityMatrixMultipliers[stake] = density
      saveSettings()
      alertAdmins(`Bot density updated.`)
    } else await supportsafeSendMessage(chatId, "Invalid setting.")
  } catch (e) { logError(e) }
})

supportBot.onText(/\/setmindensity/, async (msg) => {
  const chatId = msg.chat.id;
  if (!ADMINS.includes(chatId)) return
  try {
    const [, densityStr] = msg.text.split(' ')
    const density = parseInt(densityStr)

    if (!isNaN(density) ) {
      SETTINGS.minimumDensity = density
      saveSettings()
      alertAdmins(`Minumum bot density updated.`)
    } else await supportsafeSendMessage(chatId, "Invalid setting.")
  } catch (e) { logError(e) }
})

const { spawn } = require('child_process');

supportBot.onText(/\/restart/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  
  const interval = setInterval(() => {
    if (extantGames.filter(g => g.started).reduce((a, b) => a + b.selected.length, 0) == 0) {
      spawn('bash', ['-c', 'sudo killall node ; sudo git pull ; sudo nohup node server.js'], { detached: true, stdio: 'ignore' }).unref();
      //process.exit()
      clearInterval(interval)
    }
  }, 50)
})

supportBot.onText(/\/userinfo/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  
  try {
    var id = msg.reply_to_message?.from_origin?.sender_user?.id || parseInt(msg.text.split(' ')[1])
    var user
    if (!id || !(user  = await User.findOne({id}))) return await supportsafeSendMessage(chatId, "No such user..")

    var gamesPlayed = await Game.countDocuments({players:user._id, stake:{$ne:0}})
    await supportsafeSendMessage(chatId, JSON.stringify({
      gamesPlayed,
      info: user.toObject(),
      telegramInfo: await bot.getChat(id), 
    }, null, 2))
  } catch (e) { logError(e) }
})

supportBot.onText(/\/permaban/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  
  try {
    var id = msg.reply_to_message?.from_origin?.sender_user?.id || parseInt(msg.text.split(' ')[1])
    var user
    if (!id || !(user  = await User.findOne({id}))) return await supportsafeSendMessage(chatId, "No such user.")

    user.banned = !user.banned;
    await user.save()
    await supportsafeSendMessage(chatId, JSON.stringify({
      name: user.name,
      banned: user.banned 
    }, null, 2))
  } catch (e) { logError(e) }
})


supportBot.onText(/\/setwinrate/, async (msg) => {
  const chatId = msg.chat.id;
  if (!ADMINS.includes(chatId)) return
  try {
    const [, stakeStr, winRateStr] = msg.text.split(' ')
    const stake = parseInt(stakeStr)
    const winRate = parseFloat(winRateStr)

    if ([10, 20, 50, 100].includes(stake) && !isNaN(winRate) ) {
      winRate = Math.min(Math.max(winRate, 0),1)
      
      SETTINGS.botWinPercentage[stake] = winRate
      saveSettings()
      alertAdmins(`Bot win rate updated.`)
    } else await supportsafeSendMessage(chatId, "Invalid setting.")
  } catch (e) { logError(e) }
})

supportBot.onText(/\/respawn/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return
  try {
    await supportsafeSendMessage(chatId, `Respawned ${await respawnOpenGames()} games. ${extantGames.length} games active.`)
  } catch (e) { logError(e) }
})

let lastSpamMessage = ''

const sendFormattedSpam = async (msg, text, user, test) => {
  if (test) text = '[Send this message again if you are sure.]\n\n' + text
  text = text.replace('{{user}}', user.name)

  let inline_keyboard = []
  let addBackground = false

  if (text.includes('{{invite}}')) {
    text = text.replace('{{invite}}', '')
    inline_keyboard.push([{ text: 'Invite ✉️', callback_data: 'invite' }])
  }
  
  if (text.includes('{{invite_link}}')) {
    text = text.replace('{{invite_link}}', `t.me/bluebingobot?start=${user.id}`)
  }
  
  if (addBackground = text.includes('{{background}}')) {
    text = text.replace('{{background}}', ``)
  }

  if (msg.photo || addBackground) await safeSendPhoto(user.id, addBackground ? BACKGROUND_IMAGE : msg.photo[0].file_id, {
    caption: text, reply_markup: inline_keyboard.length ? { inline_keyboard } : undefined
  })
  else await safeSendMessage(user.id, text, inline_keyboard.length ? {reply_markup: { inline_keyboard }} : undefined)
}

supportBot.onText(/\/spam/, async (msg) => {
  const chatId = msg.chat.id; 
  if (!ADMINS.includes(chatId)) return

  let text = msg.text.replace('/spam', '')

  let test = text.includes('{{test}}')
  text = text.replace('{{test}}', '')

  if (test || lastSpamMessage != text) {
    lastSpamMessage = text
    await sendFormattedSpam(msg, text, await User.findOne({id: chatId}), true)
  } else {
    lastSpamMessage = ''

    await supportsafeSendMessage(chatId, "Spam sent!");

    let allUsers = await User.find({})
    while (allUsers.length) {
      await Promise.all(allUsers.splice(0, 50).map(u => sendFormattedSpam(msg, text, u)))
    }
  }
})

const alertAdmins = async (m, skip) => {
  await Promise.all(ADMINS.map(async a => {
    if (a != skip) {
      try {
        await supportsafeSendMessage(a, m)
      } catch (error) {
        console.error(`Failed to send alert to admin ${a}:`, error)
      }
    }
  }))
}

const forwardMessageContents = async (message, chatId, prefix = '') => {
  var args = {
    text: message.text
  }
  //message, audio, document, video
  if (message.text) {
    await supportsafeSendMessage(chatId, message.text)
  }
}

const messageId = (msg) => msg.chat.id + ' - ' + msg.message_id

// Customer Side
supportBot.onText(/\/start/, async (msg) => {
  const chatId = msg.chat.id; 
  await supportsafeSendMessage(chatId, "Welcome to Blue Bingo support. How can we help you?")
})
supportBot.on('message', async (msg) => {
  try {
    if (!msg.text || msg.text.startsWith('/')) return;
    const chatId = msg.chat.id; 
  
    if (ADMINS.includes(chatId)) {
      // short-term fix, could send duplicate replies to random users. todo keep list of message ids
      if (msg.reply_to_message && msg.text[0] != '/') {
        var replyUser = await User.findOne({comments: msg.reply_to_message.text})
        if (replyUser) {
          await forwardMessageContents(msg, replyUser.id)
          alertAdmins(`Admin is replying to ${replyUser.name} (${replyUser.id}}) \n\n${msg.text}`, chatId)
        }
      }
      /*
      if (msg.reply_to_message && msg.reply_to_message.forward_from) {
        var replyUser = await User.findOne({id: msg.reply_to_message.forward_from.id})
        if (replyUser) {
          await forwardMessageContents(msg, replyUser.id)
          alertAdmins(`Admin is replying to ${replyUser.name} (${replyUser.id}})`)
        }
      }*/
    } else {
      let user = await User.findOne({ id: chatId });
      if (user) {
        if (!user.lastSupportMessage) await supportsafeSendMessage(chatId, "Thank you for your feedback. A moderator will be with you shortly.")
  
        user.comments.push(msg.text)
        user.lastSupportMessage = messageId(msg)
        await user.save();
  
        ADMINS.forEach(async a => await supportBot.forwardMessage(a, chatId, msg.message_id))
        //alertAdmins(`User ${user.name} (${user.id}}):\n\n${msg.text}`)
      }
    }
  } catch (e) { logError(e) }
})

async function safeSendMessage(chatId, message, options = {}) {
  try {
    await bot.sendMessage(chatId, message, options);
  } catch (e) { console.log(e) }
}
async function safeSendPhoto(chatId, photoPath, options = {}) {
  try {
    await bot.sendPhoto(chatId, photoPath, options);
  } catch (e) { console.log(e) }
}
async function supportsafeSendMessage(chatId, message, options = {}) {
  try {
    await supportBot.sendMessage(chatId, message, options);
  }
  catch (e) { logError(e) }
}

// Game Bot
bot.on('message', async (msg) => {
  try {
    if (!msg.text) return;
    if (msg.text == 'test_error') throw new Error("Test Error");
    if (msg.text.startsWith('/')) return;

    const chatId = msg.chat.id; 
    let user = await User.findOne({ id: chatId });
    if (!user) {
      await safeSendMessage(chatId, "Press /start.")
    } else if (!user.phone) {
      if (validPhone(msg.text)) {
        await contact({
          chat: {id: chatId},
          contact: {phone_number: msg.text}
        })
      } else {
        await safeSendMessage(chatId, "Please input or share a valid phone number.")
        await askPhone(chatId)
      }
    } else if (user.withdrawal) {
      if (user.withdrawal.length == 0) {
        var amount = parseFloat(msg.text)
        if (amount > user.balance) {
          await safeSendMessage(chatId, 'You do not have enough balance to withdraw that amount.')
        } else if (amount < 100) {
          await safeSendMessage(chatId, 'The minimum withdraw amount is 100ETB. Please try agian.')
        } else if (amount < 0) {
          await safeSendMessage(chatId, 'Please enter a valid amount.')
        } else if (amount) {
          user.withdrawal.push(''+amount)
          await user.save();
          await sendBankDetails(chatId, user);
        }
      }
      else if (user.withdrawal.length == 2) {
        if (!msg.text.match(/^[A-Za-z ]+$/) || msg.text.length <= 3) {
          return await safeSendMessage(chatId, `Invalid name. Must be in English and above 3 characters.`)
        }

        var bankName = getBankName(user.withdrawal[1])
        user.withdrawal.push(msg.text)
        await user.save();
        await safeSendMessage(chatId, `Please send the account number for ${msg.text} @ ${bankName}.`)
      } else if (user.withdrawal.length == 3) {
        var amount = parseFloat(msg.text)
        if (amount && amount > 0) {
          user.withdrawal.push(msg.text)
          await user.save();
          await sendWithdrawalConfirmation(chatId, user);
        } else {
          await safeSendMessage(chatId, "Invalid withdrawal amount.")
        }
      }
    } else {
      if (user.expecting === 'name') {
        user.name = msg.text
        user.expecting = undefined
        user.save()
        await safeSendMessage(chatId, `Your name is now ${user.name}`);
      } else if (user.expecting === 'deposit_number') {
        let phone = validPhone(msg.text);
        if (phone && (await User.exists({phone}))) {
          user.deposit_phone = phone
          //user.deposit_user = (await User.findOne({phone}))._id
          user.expecting = 'deposit_amount'
          await safeSendMessage(chatId, 'Please send the amount to deposit:');
          await user.save()
        } else {
          await safeSendMessage(chatId, 'No user found with the provided phone number in our database.\n Please register the phone number first and try again.')
        }
      } else if (user.expecting === 'deposit_amount') {
        let amount = Math.abs(Math.floor(parseInt(msg.text)));
        if (amount) {
          const transaction = await Transaction.create({
            from: user._id,
            amount
          })

          user.expecting = undefined;
          await user.save()
          await transaction.save()

          try {
            const response = await axios.post('https://api.chapa.co/v1/transaction/initialize', {
              key: process.env.CHAPA_PRIVATE_KEY,
              amount,
              tx_ref: transaction._id,
              currency: 'ETB',
              phone_number: user.deposit_phone,
              callback_url: url+'/chapa',
              return_url: url+'/choose-board'
            }, {
              headers: {
                'Authorization': `Bearer ${process.env.CHAPA_PRIVATE_KEY}`,
                'Content-Type': 'application/json'
              }
            });

            if (!response.data || !response.data.data) {
              console.error('Chapa API error:', response.data);
              await safeSendMessage(chatId, `Chapa API failed. Please try again.`)
            } else {
              await safeSendMessage(chatId, `Payment details\n<pre>Name:${user.name}\nPhone:${user.deposit_phone}\nAmount:${amount}ETB\nreference: ${transaction._id}</pre>`, {parse_mode: 'html', reply_markup: {
                inline_keyboard: [[{
                  text: `Pay ${amount}ETB`,
                  web_app: {
                    url: response.data.data.checkout_url
                  }
                }],[{
                  text: 'Verify',
                  callback_data: 'verify_'+transaction._id
                }]]
              }})
            }
          } catch (error) {
            console.error('Chapa payment initialization error:', error);
            await safeSendMessage(chatId, `Payment initialization failed. Please try again.`)
          }
          
        } else {
          await safeSendMessage(chatId, 'Please enter a valid amount.')
        }
      } else if (user.expecting == 'transfer_number') { //transfer logic, since we don't need to utilize chapa for such transactions
        let phone = validPhone(msg.text);
        // always fail early and with variety
        if (!phone || !(await User.exists({phone}))) {
          await safeSendMessage(chatId, 'No user found with the provided phone number in our database.\n Please use a registered phone number and try again.')
        } else if (phone == user.phone) {
          await safeSendMessage(chatId, 'This is your number, please provide a different number and try again.')
        } else {
          user.transfer_user = (await User.findOne({phone}))._id
          user.expecting = 'transfer_amount'
          await user.save();
          await safeSendMessage(chatId, 'Please enter the amount to transfer:');
        }
      } else if (user.expecting == 'transfer_amount') {
        let amount = Math.abs(Math.floor(parseInt(msg.text)));
        if (!amount) {
          await safeSendMessage(chatId, 'Please enter a valid amount to transfer.')
        } else if (amount < 100) {
          await safeSendMessage(chatId, 'Minimum transfer amount is 100ETB. Please try again.')
        } else if (amount > user.balance) {
          user.expecting = undefined
          await user.save();
          await safeSendMessage(chatId, 'You do not have sufficient balance for this tranfer.')
        } else {
            const otherUser = await User.findById(user.transfer_user);
            const transaction = await Transaction.create({
              from: user._id, // use MongoDB _id, not Telegram id
              to: user.transfer_user,
              amount: amount,
              complete: true // no verification needed
            })

            user.expecting = undefined // reset bot state

            user.balance -= amount
            otherUser.balance += amount

            await user.save()
            await otherUser.save()
            await transaction.save()

            // send message to both parties
            var message = `Transfer Report<pre style="text-align: center;">From phone:${user.phone}\nTo phone:${otherUser.phone}\nAmount:${amount}ETB</pre>`
            await safeSendMessage(chatId, message, {parse_mode: 'html'})
            // .id is Telegram id. not to be confused with MongoDB's ._id
            await safeSendMessage(otherUser.id, message, {parse_mode: 'html'})
        }
      }
    }
  } catch (e) {
    logError(e)
  }
})

const checkBan = async (user) => {
  if (user?.banned) {
    await safeSendMessage(user.id, 'You have been banned from playing Blue Bingo. Please contact support for more information.')
    return true
  }
}

const play = async (msg) => {
  const chatId = msg.chat.id;
  const user = await User.findOne({ id: chatId })
  if (await checkBan(user)) return;

  if (!user) {
    return await safeSendMessage(chatId, '❌ Please register first by pressing /start')
  }

  const totalBalance = user.balance + user.bonusBalance

  await safeSendMessage(chatId, `🎮 <b>Choose Your Game</b>\n\n💰 Your Balance: ${totalBalance.toFixed(2)} ETB\n\n� Select a stake level to start playing:`, {
    parse_mode: 'html',
    reply_markup: {
      inline_keyboard: [
        [
          {text: `🎮 Play 10 ETB ${totalBalance >= 10 ? '✅' : '❌'}`, web_app: {url: url+'/choose-board?stake=10'}},
          {text: `🎮 Play 20 ETB ${totalBalance >= 20 ? '✅' : '❌'}`, web_app: {url: url+'/choose-board?stake=20'}}
        ],
        [
          {text: `🎮 Play 50 ETB ${totalBalance >= 50 ? '✅' : '❌'}`, web_app: {url: url+'/choose-board?stake=50'}},
          {text: `🎮 Play 100 ETB ${totalBalance >= 100 ? '✅' : '❌'}`, web_app: {url: url+'/choose-board?stake=100'}}
        ],
        [
          {text: '� Play Demo (Free)', web_app: {url: url+'/choose-board?stake=0'}}
        ],
        [
          {text: '💸 Add Funds', callback_data: 'deposit'},
          {text: '💰 Check Balance', callback_data: 'balance'}
        ]
      ]
    }
  })
}
const deposit = async (msg) => {
  const chatId = msg.chat.id;
  let user = await User.findOne({ id: chatId });
  if (await checkBan(user)) return;

  if (user) {
    if (await checkPhone(chatId, user)) return;
    await safeSendMessage(chatId, '💸 <b>Add Funds to Your Account</b>\n\n📱 Please enter the phone number you used to make your deposit.\n\n💡 <i>Make sure to use the same phone number that you used for the payment.</i>', {
      parse_mode: 'html',
      reply_markup: {
        remove_keyboard: true,
        inline_keyboard: [
          [{ text: '❌ Cancel', callback_data: 'cancel_deposit' }]
        ]
      }
    })
    user.expecting = 'deposit_number'
    user.withdrawal = undefined
    await user.save()
  } else {
    await safeSendMessage(chatId, '❌ Account not found. Please press /start to register.')
  }
}
const balance = async (msg) => {
  const chatId = msg.chat.id;
  let user = await User.findOne({ id: chatId });
  if (await checkBan(user)) return;
  if (user) {
    const totalBalance = user.balance + user.bonusBalance
    const gamesPlayed = await Game.countDocuments({players: user._id, stake: {$ne: 0}})

    await safeSendMessage(chatId, `💰 <b>Account Balance</b>\n\n👤 <b>Name:</b> ${user.name}\n📱 <b>Phone:</b> ${user.phone || 'Not registered'}\n\n💵 <b>Main Balance:</b> ${user.balance.toFixed(2)} ETB\n🎁 <b>Bonus Balance:</b> ${user.bonusBalance.toFixed(2)} ETB\n💎 <b>Total Balance:</b> ${totalBalance.toFixed(2)} ETB\n\n🎮 <b>Games Played:</b> ${gamesPlayed}\n\n${totalBalance > 0 ? '🎯 Ready to play!' : '💸 Add funds to start playing!'}`, {
      parse_mode: 'html',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🎮 Play Game', callback_data: 'play' },
            { text: '💸 Deposit', callback_data: 'deposit' }
          ],
          [
            { text: '💰 Withdraw', callback_data: 'withdraw' },
            { text: '✉️ Invite Friends', callback_data: 'invite' }
          ]
        ]
      }
    })
  } else {
    await safeSendMessage(chatId, '❌ Account not found. Please press /start to register.')
  }
}

const transfer = async (msg) => {
  const chatId = msg.chat.id;
  let user = await User.findOne({ id: chatId });
  if (await checkBan(user)) return;
  if (!user) {
    await safeSendMessage(chatId, 'Press /start.')
  } else {
    user.expecting = 'transfer_number'
    await user.save()
    await safeSendMessage(chatId, 'Provide the number you wish to transfer to:')
  }
}

bot.onText(/\/play/, play)
bot.onText(/\/deposit/, deposit)
bot.onText(/\/balance/, balance)
bot.onText(/\/register/, contact)
bot.onText(/\/invite/, invite)
bot.onText(/\/instruction/, instructions)
bot.onText(/\/instructions/, instructions)
bot.onText(/\/transfer/, transfer)

bot.onText(/\/change_name/, async (msg) => {
  const chatId = msg.chat.id; 
  let user = await User.findOne({ id: chatId });
  if (user) {
    user.expecting = 'name'
    await user.save()
  }
  await safeSendMessage(chatId, 'Please enter your new name.')
})

const BANKS = [
  [
    ['TeleBirr', 'telebirr', 855], ['Dashen', 'dashen_bank', 880]
  ],
  [
    ['M-Pesa', 'mpesa', 266], ['Awash', 'awash_bank', 656]
  ],
  [
    ['CBEBirr', 'cbebirr', 128], ['Bank of Abyssinia', 'boa_bank', 347]
  ]
]


const sendBankDetails = async (chatId, user) => {
  await safeSendMessage(chatId, "Please select a bank to withdraw your money.", {
    reply_markup: {
      inline_keyboard: BANKS.map(row => row.map(bank => ({
        text: bank[0],
        callback_data: 'bank_'+bank[2]+'_'+bank[1]
      })))}
  });
}

const getBankName = (id) => {
  var res = ''
  BANKS.forEach(row => row.forEach( bank => {
    if (
      bank[1] == id || 
      bank[2] == id || 
      bank[2]+'' == id
    ) res = bank[0]
  }))
  return res
}

const sendWithdrawalConfirmation = async (chatId, user) => {
  const [amount, bank, name, number] = user.withdrawal;
  const bankCode = parseInt(bank);
  const withdrawAmount = parseFloat(amount)

  await safeSendMessage(chatId, `💰 <b>Confirm Withdrawal</b>\n\n📋 <b>Withdrawal Details:</b>\n🏦 <b>Bank:</b> ${getBankName(bankCode)}\n👤 <b>Account Name:</b> ${name}\n🔢 <b>Account Number:</b> ${number}\n💵 <b>Amount:</b> ${withdrawAmount.toFixed(2)} ETB\n\n⚠️ <i>Please double-check all details before confirming. This action cannot be undone.</i>`, {
    parse_mode: 'html',
    reply_markup: {
      inline_keyboard: [
        [{
          text: `✅ Confirm Withdrawal (${withdrawAmount.toFixed(2)} ETB)`,
          callback_data: 'confirm_withdrawal'
        }],
        [{
          text: '❌ Cancel Withdrawal',
          callback_data: 'cancel_withdrawal'
        }]
      ]
    }
  })
}


const checkPhone = async(chatId, user) => {
  if (!user.phone) {
    await askPhone(chatId)
    return true
  }
  return false
}
bot.onText(/\/withdraw/, async (msg) => {
  const chatId = msg.chat.id; 
  let user = await User.findOne({ id: chatId });

  if (!user || await checkPhone(chatId, user)) return;

  if (user) {
    user.withdrawal = []
    await safeSendMessage(chatId, 'Please enter the amount to withdraw.')
    await user.save()
  }
})

const logError = (e) => {
  console.log(e)
  var message = e.toString() + '\n\n' + e.stack.toString()
  ADMINS.forEach(async admin => {
    try {
      await supportsafeSendMessage(admin, message)
    } catch (e) {}
    try {

      const formData = new FormData();
      formData.append('chat_id', admin);
      formData.append('document', Buffer.from(message, 'utf-8'), { filename: 'err.txt', contentType: 'plain/text' });

      await axios.post(`https://api.telegram.org/bot${process.env.SUPPORT_TELEGRAM_TOKEN}/sendDocument`, formData, {
          headers: {
              ...formData.getHeaders(),
          },
      })
    } catch (e) {
      console.log(e)
    }
  })
}

bot.onText(/\/support/, async (msg) => {
  const chatId = msg.chat.id;
  try {
    await safeSendPhoto(chatId, BACKGROUND_IMAGE, {
      caption: '🕿 <b>Need Help?</b>\n\nOur support team is here to help you 24/7!\n\n💬 Click the button below to contact support.',
      parse_mode: 'html',
      reply_markup: {
        inline_keyboard: [
          [{ text: '🕿 Contact Support', url: 'https://t.me/bluebingosupportbot' }],
          [{ text: '📖 How to Play', web_app: {url} }],
          [{ text: '🔙 Back to Menu', callback_data: 'back_to_menu' }]
        ]
      }
    })
  } catch (e) {
    logError(e)
  }
})

bot.onText(/\/stats/, async (msg) => {
  const chatId = msg.chat.id;
  try {
    const user = await User.findOne({ id: chatId });
    if (!user) {
      return await safeSendMessage(chatId, '❌ Please register first by pressing /start')
    }

    const gamesPlayed = await Game.countDocuments({players: user._id, stake: {$ne: 0}})
    const gamesWon = await Game.countDocuments({winner: user._id})
    const totalWinnings = await Transaction.aggregate([
      { $match: { to: user._id, complete: true } },
      { $group: { _id: null, total: { $sum: "$amount" } } }
    ])
    const winnings = totalWinnings[0]?.total || 0

    const winRate = gamesPlayed > 0 ? ((gamesWon / gamesPlayed) * 100).toFixed(1) : 0

    await safeSendMessage(chatId, `📊 <b>Your Game Statistics</b>\n\n👤 <b>Player:</b> ${user.name}\n🎮 <b>Games Played:</b> ${gamesPlayed}\n🏆 <b>Games Won:</b> ${gamesWon}\n📈 <b>Win Rate:</b> ${winRate}%\n💰 <b>Total Winnings:</b> ${winnings.toFixed(2)} ETB\n💵 <b>Current Balance:</b> ${(user.balance + user.bonusBalance).toFixed(2)} ETB`, {
      parse_mode: 'html',
      reply_markup: {
        inline_keyboard: [
          [{ text: '🎮 Play Again', callback_data: 'play' }],
          [{ text: '💰 Check Balance', callback_data: 'balance' }]
        ]
      }
    })
  } catch (e) {
    logError(e)
  }
})

let _creatingId = []

bot.onText(/\/start/, async (msg) => {
  const chatId = msg.chat.id; 
  try {
    let user = await User.findOne({ id: chatId });
    
    const welcomeMessage = user ?
      `🎉 Welcome back, ${user.name}!\n\n💰 Balance: ${(user.balance + user.bonusBalance).toFixed(2)} ETB\n🎮 Ready to play some Bingo?` :
      `🎉 Welcome to Blue Bingo!\n\n🎯 The most exciting Bingo game in Ethiopia!\n💰 Win real money playing Bingo\n🎮 Easy to play, fun to win!`

    await safeSendPhoto(chatId, BACKGROUND_IMAGE, {
      caption: welcomeMessage,
      reply_markup: {
        inline_keyboard: user ? [
          [{ text: '🎮 Play Game', callback_data: 'playbutton'}, { text: '💰 Check Balance', callback_data: 'balance' }],
          [{ text: '💸 Deposit Funds', callback_data: 'deposit' }, { text: '💰 Withdraw', callback_data: 'withdraw' }],
          [{ text: '✉️ Invite Friends', callback_data: 'invite' }, { text: '🕿 Support', url: 'https://t.me/bluebingosupportbot' }],
          [{ text: '📖 How to Play', web_app: {url} }]
        ] : [
          [{ text: '🎮 Play Game', callback_data: 'playbutton'}, { text: '📝 Register', callback_data: 'register'}],
          [{ text: '💰 Check Balance', callback_data: 'balance' },{ text: '💸 Deposit', callback_data: 'deposit' }],
          [{ text: '🕿 Contact Support', url: 'https://t.me/bluebingosupportbot' },{ text: '📖 Instructions',  web_app: {url} }],
          [{ text: '✉️ Invite Friends', callback_data: 'invite' }]
        ]
      }
    })

    if (!user) {
      if (!_creatingId.includes(chatId)) {
        _creatingId.push(chatId)
        try {
          var referral = parseInt(msg.text.substring(7)) || undefined
          user = await User.create({ id: chatId, name: msg.from.first_name, referral, bonusBalance: STARTING_BALANCE });
          await user.save();
          await safeSendMessage(chatId, `Welcome to Blue Bingo, ${msg.from.first_name}.`)
        } catch (e) {
          logError(e)
        }
        _creatingId = _creatingId.filter(x => x != chatId)
      }
    } else if (!user.phone) {
      await safeSendMessage(
        chatId, 
        `To complete your registration, please click the 'Share Contact' button below to share your phone number. You can also use /change_name to change your name.`,
        {
          reply_markup: {
            keyboard: [
              [{
                text: 'Share Contact',
                request_contact: true
              }]
            ]
          }
        }
      );
    }
  } catch(e) {
    logError(e)
  }
});

const games = new Map()
let extantGames = []
const lobbySessions = new Map()

function newCartela(seedGame) {
  const rand = gen.create(seedGame._id.toString())
  const cartela = []

  for (var i = 0; i < 100; i++) {
    const added = []
    var card = []
    for (var _i = 0; _i < 5; _i++) {
      var c = []
      for (var _j = 0; _j < 5; _j++) {
        if (_i == 2 && _j == 2) {
          c.push('*')
        } else {
          var num
          do {
            num = 1 + rand.range(15) + _j*15
          } while (added.includes(num))
          c.push(num)
          added.push(num)
        }
      }  
      card.push(c)
    }

    cartela.push(card)
  }

  return cartela
}

Object.keys(stakes).forEach(async s => {
  const g = await newGame(s)
  games.set(''+s, g)
  await startSpawningBots(games.get(''+g.stake))
})

const ws = require('ws');
const wss = new ws.Server({ noServer: true })
server.on('upgrade', function(request, socket, head, ){
  wss.handleUpgrade(request, socket, head, (ws) => {
    wss.emit('connection', ws, request)
  })
})

function getUserId(auth) {
  // Decode the initData string
  try {
    if (!auth) return null

    const encoded = decodeURIComponent(auth);

    // Extract the hash value from the initData
    const arr = encoded.split('&');
    const hashIndex = arr.findIndex(str => str.startsWith('hash='));

    if (hashIndex === -1) {
      console.error('No hash found in auth data')
      return null
    }

    const hash = arr.splice(hashIndex)[0].split('=')[1];
    const userIndex = arr.findIndex(str => str.startsWith('user='));

    if (userIndex === -1) {
      console.error('No user data found in auth')
      return null
    }

    const user = JSON.parse(arr[userIndex].split('=')[1]);

    // Prepare the data-check-string
    const sortedArr = [...arr].sort().join('\n');
    const dataCheckString = `${sortedArr}`;

    // Generate the HMAC-SHA-256 signature of the data-check-string with the secret key
    const secretKey = crypto.createHmac('sha256', 'WebAppData').update(TOKEN).digest();
    const hmac = crypto.createHmac('sha256', secretKey).update(dataCheckString).digest('hex');

    // Compare the generated hash with the received hash
    const isValid = hmac === hash
    if (!isValid) {
      console.error('Invalid auth hash')
      return null
    }

    return user.id

  } catch (error) {
    console.error('Error parsing auth data:', error)
    return null
  }
}

const canDeductBalance = (player, amount) => (player.balance + player.bonusBalance) >= amount

const deductBalance = (player, amount) => {
  const bonusDeduction = Math.min(player.bonusBalance, amount)
  const balanceDeduction = amount - bonusDeduction
  
  player.balance -= balanceDeduction
  player.bonusBalance -= bonusDeduction

  return balanceDeduction
}

const addReferralBonus = async (player) => {
  try {
    const refUser = await User.findOne({ id: player.referral });
      if (refUser) {
        refUser.bonusBalance += STARTING_BALANCE
        player.paidReferral = true
        await refUser.save();
        await player.save();
        await safeSendMessage(player.referral, `${player.first_name} just played their first game. You have received ${STARTING_BALANCE}ETB bonus and will receive 10% of all their future wins.`)
      }
  } catch (e) {logError(e)}
}

async function assignGame(g, game, player, selected, isBot = false) {
  if (g) {
    if (isBot) {
      g.botNumbers = g.botNumbers.filter(x => x != selected)
      g.joinedBotNumbers.push(selected)
    } else {
      var transaction = new Transaction({from: player._id, amount: game.stake, complete: true})
      g.realMoney += deductBalance(player, game.stake)

      if (RECEIVE_BONUS_WHEN == "play_first_game" && !player.paidReferral && game.stake && player.referral) await addReferralBonus(player)

      game.players.push(player);
      game.numbers.push(selected)
      g.selected.push(selected)
      
      await transaction.save()
      await player.save()
    }

    g.b({
      joined: selected
    })
    if (canStartLobby(game.stake) && getAllJoinedNumbers(g).length > 1) {
      g.started = true;
      game.started = true
    }

    g.b({
      derash: calculateDerash(getAllJoinedNumbers(g).length, game.stake),
    })
    g.b({
      totalPlayers: getAllJoinedNumbers(g).length
    })
    await game.save()
  }
}


// Handle new WebSocket connections
wss.on('connection', (ws) => {
  ws.jsend = (x) => {
    try {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify(x))
      }
    } catch (error) {
      console.error('WebSocket send error:', error)
    }
  }

  ws.on('close', () => {disconnectPlayer(ws)})

  ws.on('error', (error) => {
    console.error('WebSocket error:', error)
    disconnectPlayer(ws)
  })

  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message);
      if (process.env.DEV) console.log('WebSocket message:', data)


    if (!ws.authenticated) {
      const userId = getUserId(data.auth)
      if (process.env.DEV) console.log('User ID from auth:', userId)

      if (!userId) {
        ws.jsend({error: 'Invalid authentication'})
        return
      }

      const user = await User.findOne({id: userId})
      if (user) {
        ws.authenticated = user._id
        lobbySessions.forEach((data, sock) => {
          if (!data || data.userId == userId) {
            disconnectPlayer(sock)
          }
        })
        
        if(games.get(''+data.stake) == undefined) {
          ws.jsend({redirect: '/choose-board?stake=0'})
        } else if (data.page == '/bingo') {
          var g = extantGames.find((g) => g.id == data.gameId)
          var game = await Game.findById(asId(data.gameId))

          if (!game) ws.jsend({redirect: '/choose-board?stake='+data.stake})
          else if (game.banned.includes(ws.authenticated)) ws.jsend({kick: true})
          else if (game.winner || game.botWon) {
            var init = {
              init: true,
              numPlayers: Math.min(100, game.players.length + getBotDensity(game)), 
              gameNumber: game._id.toString(), 
              selectedNumber: getWinnerNumber(game), 
              cartelaBoard: getWinnerBoard(game),
              winnerName: await getWinnerName(game),
              winnerBoard: getWinnerBoard(game),
              winnerNumber: getWinnerNumber(game),
              winnerPatterns: checkWin(game.calledNumbers, getWinnerBoard(game)),
              calledNumbers: (game.calledNumbers.length > 0 ? game.calledNumbers : (g.calledNumbers || []))
            }
            ws.jsend(init)
          } else if (g && game.players.includes(ws.authenticated)) {
            g.broadcast.push(ws)
            var selectedNumber = game.numbers[game.players.indexOf(ws.authenticated)]
            var init = {
              init: true,
              numPlayers: getAllJoinedNumbers(g).length, 
              gameNumber: game._id.toString(), 
              selectedNumber, 
              cartelaBoard: newCartela(game)[selectedNumber-1],
              calledNumbers: (game.calledNumbers.length > 0 ? game.calledNumbers : (g.calledNumbers || []))
            }
            ws.jsend(init)
          } else ws.jsend({redirect: "/choose-board?stake="+game.stake})

        } else if (data.page == '/choose-board') {

          lobbySessions.set(ws, {
            userId,
            name: user.name,
            page: data.page,
            stake: data.stake,
          })

          var g = games.get(''+data.stake)
          g.broadcast.push(ws)


          ws.jsend({
            balance: user.balance,
          })

          ws.jsend({
            bonusBalance: user.bonusBalance,
          })

          ws.jsend({
            cartela: g.cartela
          })

          ws.jsend({
            activeGames: extantGames.filter(_g => _g.stake == g.stake).length - 1
          })

          ws.jsend({
            allSelected: getAllSelectedNumbers(g)
          })

          ws.jsend({
            allJoined: getAllJoinedNumbers(g)
          })

        }
      } else {
        ws.jsend({error: 'Invalid User'})
      }

    } else {
      var session = lobbySessions.get(ws)
      var game = session ? games.get(''+session.stake) : undefined

      if(data.join || data.join == 0) {
        if (!session) return ws.jsend({redirect: '/choose-board?stake=0'})

        var gameDoc = await Game.findById(asId(game.id))
        var player = await User.findById(ws.authenticated)
        if (!session.selected) {
          ws.jsend({notify: 'No number selected.'})
        } else if (!gameDoc || !game || gameDoc.winner || !player) {
          ws.jsend({redirect: '/choose-board?stake='+session.stake})
        } else if (gameDoc.players.includes(player._id)) {
          ws.jsend({redirect: '/bingo?gameId='+gameDoc._id.toString()})
        } else if (game.calledNumbers) {
          ws.jsend({alreadyStarted: true})
        } else if (!canDeductBalance(player, gameDoc.stake)) {
          ws.jsend({notify: 'Not enough money.'})
        } else if (!canStartLobby(game.stake)) {
          ws.jsend({notify: "Lobby is full, wait until game finishes"})
          if (!games.get(''+game.stake).waitingForLobby.includes(ws)) games.get(''+game.stake).waitingForLobby.push(ws)
        } else {
          await assignGame(game, gameDoc, player, session.selected)
          ws.jsend({redirect: '/bingo?gameId='+gameDoc._id.toString()})
        }
      } else if (data.selected) {
        if (!session) return ws.jsend({redirect: '/choose-board?stake=0'})

        if (getAllSelectedNumbers(game).includes(data.selected)) {
          ws.jsend({
            selected: data.selected
          })
        } else { 
          lobbySessions.forEach((s, sock) => { 
            if (session.stake != s.stake || sock == ws) return 
            sock.jsend({
              unselected: session.selected
            }) 
            sock.jsend({
              selected: data.selected
            })
          })
          session.selected = data.selected
        }

      } else if (data.unselected) {
        if (!session) return ws.jsend({redirect: '/choose-board?stake=0'})
          
        if (session.selected == data.unselected) {
          session.selected = undefined;
          lobbySessions.forEach((s, sock) => { if (session.stake == s.stake) sock.jsend({ unselected: data.unselected }) })
        }
      } else if (data.bingo) {
        var game = await Game.findById(asId(data.gameId))
        var g = extantGames.find((g) => g.id == game._id.toString())
        
        if (!game) {
          throw new Error('Bingo on game that doesn\'t exist.');
        } else if (!g || game.winner) {
          ws.jsend({notify: 'This game is over'})
        } else if (game.banned.includes(ws.authenticated)) {
          throw new Error('Banned player is spamming Bingo.')
        } else if (game.players.includes(ws.authenticated)) {
          if (!g.calledNumbers) {
            ws.jsend({notify: "Wait until game starts. If you press Bingo without a bingo, you will be kicked from the game."})
          } else if (checkWin(g.calledNumbers, g.cartela[game.numbers[game.players.indexOf(ws.authenticated)] - 1])) {
            await selectWinner(game, ws.authenticated, g)
          } else {
            ws.jsend({kick: true})
            game.banned.push(ws.authenticated)
            await game.save();
          }
        }
      }
    }
  } catch(e) {logError(e)}});
});


/*
  


*/

/*
/check_transaction - Chapa transaction number
*/  

const getFakeUsername = (seedGame) => {
  const rand = gen.create(seedGame._id.toString())
  return botUsernames[rand.range(botUsernames.length)].split(' ')[0]
}
function getRandomInRange(min, max) {
  const randomGenerator = gen.create();
  return Math.max(SETTINGS.minimumDensity || 0, Math.ceil(randomGenerator.range(max - min + 1) + min));
}

function getGMT3Time(date) {
  const utcHour = date.getUTCHours(); // Get the current hour in UTC (24-hour format)
  const utcDay = date.getUTCDay(); // Get the current day in UTC (0 = Sunday, 1 = Monday, ...)

  const gmt3Hour = (utcHour + 3) % 24; // This ensures 24-hour format
  let gmt3Day = utcDay;

  if (utcHour + 3 >= 24) {
    gmt3Day = (utcDay + 1) % 7; // Move to the next day, wrap around if necessary
  }

  return [gmt3Day, gmt3Hour]
}

function getBotDensity(game) {
  const [day, hour] = getGMT3Time(game._id.getTimestamp())
  const currentMinute = new Date().getMinutes();

  let blockIndex = Math.floor(hour/3);
  
  baseDensity = Math.floor(densityMatrix[blockIndex][day] * 0.9 * (SETTINGS.densityMatrixMultipliers[game.stake] || 0));
  
  if (currentMinute % 5 === 0) return getRandomInRange(baseDensity-5, baseDensity+5)
  else return getRandomInRange(baseDensity-1, baseDensity+2)
}
// bot wins for sure 30% of the time, the other 70% is random (so expected 67.5% win rate)
const shouldBotWin = (seedGame) => Math.random() <= (SETTINGS.botWinPercentage[seedGame.stake] || 0)

async function startSpawningBots(g) {

  if (!SETTINGS.densityMatrixMultipliers[g.stake]) return

  var game = await Game.findById(asId(g.id))
  var density = getBotDensity(game);
  density = getRandomInRange(density-1, density+1);
  console.log(`Density : ${density} - ${g.stake}`);
  var winningNumber = getWinningNumber(game)

  console.log(`Winning Number: ${winningNumber} - ${g.stake}`)

  for (var i = 0; i < density; i++) {
    setTimeout(async () => {
      var selectedNumbers = getAllSelectedNumbers(g)
      if (selectedNumbers.length < 100 - density) {
        var botWin = shouldBotWin(game) && !selectedNumbers.includes(winningNumber)

        var n
        do {
          n = botWin ? winningNumber : Math.floor(Math.random()*100)+1
          botWin = false
        } while (selectedNumbers.includes(n))
        
        g.botNumbers.push(n)
        g.b({
          selected: n
        })

        setTimeout(
          async () => await assignGame(g, await Game.findById(asId(g.id)), undefined, n, true),
          1000*(Math.random()*1+2)
        )
      }
    }, 1000*(Math.random()*3+3))    
  }
}

function getAllSelectedNumbers(g) {
  return g.broadcast.map(s => (lobbySessions.get(s) || {}).selected).filter(x => x).concat(g.selected).concat(g.botNumbers).concat(g.joinedBotNumbers)
}
function getAllJoinedNumbers(g) {
  return g.selected.concat(g.joinedBotNumbers)
}

function disconnectPlayer(ws) {
  try {
    const session = lobbySessions.get(ws)
    if (session && session.selected) {
      lobbySessions.forEach((s, sock) => {
        if (session.stake != s.stake || sock == ws) return
        try {
          sock.jsend({
            unselected: session.selected
          })
        } catch (error) {
          console.error('Error sending unselected message:', error)
        }
      })
    }

    extantGames.forEach(g => {
      g.broadcast = g.broadcast.filter(p => p != ws)
      g.waitingForLobby = g.waitingForLobby.filter(p => p != ws)
      if (ws.authenticated && !g.pingOnTelegram.includes(ws.authenticated)) {
        g.pingOnTelegram.push(ws.authenticated)
      }
    })

    lobbySessions.delete(ws)

    if (ws.readyState !== ws.CLOSED) {
      ws.close()
    }
  } catch (error) {
    console.error('Error disconnecting player:', error)
  }
}
function calculateDerash(numPlayers, stake) {
  
  return numPlayers*stake*9/10
}

const CALL_WAIT = 5
const COUNTDOWN = 30
const SPEED = process.env.DEV ? 4 : 1

var now = Date.now()

const getGamesWithStake = (s) => extantGames.filter(g => g.stake.toString() == s.toString())
const canStartLobby = (s) => (getAllSelectedNumbers(games.get(''+s)).length == 100) || (getGamesWithStake(s).filter(g => getAllSelectedNumbers(g).length != 100).length <= 1)

const getDrawNumbers = (seedGame) => {
  const rand_draw = gen.create(seedGame._id.toString()+RAND_DRAW_SEED)
  let draws = []

  while (draws.length < 75) {
    var n
    while (!n || draws.includes(n)) {
      n = rand_draw.range(75)+1
    }
    draws.push(n)
  }
  return draws
}

const getWinningNumber = (seedGame) => {
  var cartela = newCartela(seedGame)
  var future_draws = getDrawNumbers(seedGame)
  var skips = NUMBER_OF_WINNING_NUMBERS_TO_SKIP

  var draws = ['*']
  while (future_draws.length) {
    draws.push(future_draws.splice(0, 1)[0])
    for (var i = 1; i <= 100; i++) {
      if (checkWin(draws, cartela[i-1])) {
        if (skips > 0) skips--
        else return i
      }
    }
  }

  return 23
}

async function main() {
  const dt = (Date.now() - now)*SPEED/1000
  now = Date.now()

  try {
    extantGames.filter(g => g.waitingForLobby.length > 0 || g.pingOnTelegram.length > 0).forEach(g => {
      if (canStartLobby(g.stake)) {
        g.pingOnTelegram.forEach(async p => {
          if (!p) return
          try {
            const user = await User.findById(p)
            if (!user) return

            // Temporarily disabled to prevent bot spamming - TODO: implement proper rate limiting
            // safeSendMessage(user.id, "The lobby is open. You can start the game now.", {
            //   reply_markup: {
            //     inline_keyboard: [[{ text: `Play ${g.stake}🎮`, web_app: {url: url+'/choose-board?stake='+g.stake}}]]
            //   }
            // })
          } catch (e) { logError(e) }
        })
        g.waitingForLobby.forEach(ws => {
          ws.jsend({notify:"The lobby is open. You can start the game now."})
        })

        g.waitingForLobby = []
        g.pingOnTelegram = []
      }
    })
  } catch(e) { logError(e)}

  const startedGames = extantGames.filter(g => g.started)

  const gameLoop = async (g) => {
    try {
      const s = Math.floor(g.countdown)
      g.countdown -= dt
      const ns = Math.floor(g.countdown)
      if (s != ns && ns >= 0) {
        g.b({
          countdown: ns
        })
      }


      if (!g.calledNumbers) {
        if (g.countdown < 0) {
          g.calledNumbers = []

          const newLobby = await newGame(g.stake)
          games.set(''+g.stake, newLobby)
          lobbySessions.forEach((data, sock) => {
            if (data.stake == g.stake) {
              sock.jsend({allSelected: []})
              sock.jsend({cartela: newLobby.cartela})
              sock.jsend({activeGames: extantGames.filter(_g => _g.stake == g.stake).length - 1})
            }
          })
        }
      } else {
        if (g.countdown < 0) {
          g.countdown = CALL_WAIT
          const rand_draw = gen.create(g.id+RAND_DRAW_SEED)
          let n
          while (!n || g.calledNumbers.includes(n)) {
            n = rand_draw.range(75)+1
          }

          g.calledNumbers.push(n)
          if (g.calledNumbers.length >= 10 || true) { //check if current call is > 10 for bots to win
            g.joinedBotNumbers.forEach(jn => {
              if (!g.botsCalledBingos.includes(jn) && checkWin(g.calledNumbers, g.cartela[jn-1])) {
                g.botsCalledBingos.push(jn)
                setTimeout(
                  async () => {
                    if (!g.alreadyWon) {
                      console.log('Bot Won! ',g.stake)
                      await selectWinner(await Game.findById(asId(g.id)), undefined, g, jn)
                    }
                  },
                  1000*(Math.random()*0.8)
                );
              }
            });
          }
    
          if (n >= 0) {
            g.b({
              number: n
            })
          }

          if (g.calledNumbers.length >= 75) {
            await finishMaxGame(g);
          }
        }
      }
    } catch (e) { logError(e) }
  }

  await Promise.all(startedGames.map(g => gameLoop(g)))
  setTimeout(main, 100);
}

  const deleteGame = (g) => {
  g.alreadyWon = true
  extantGames = extantGames.filter(_g => _g != g)
  games.forEach((_g, k) => { if (_g == g) games.delete(k) })

  /*g.broadcast.forEach(s => { 
    try {
      s.close() 
    } catch {}
  })*/
}

async function selectWinner(game, player, g, winnerBotNumber) {
  if (g.alreadyWon) return
  g.alreadyWon = true

  if (winnerBotNumber) {
    player = {}
    game.botWon = true
    game.numbers.push(winnerBotNumber)
    
    if (game.stake > 0 && game.players.length > 0) alertAdmins(`+${game.stake*game.players.length} ETB.`)
    profitSinceBotStarted += g.realMoney
  } else {
    player = await User.findById(player)
    var gamesPlayed = await Game.countDocuments({players:player._id, stake:{$ne:0}})
  
    var win = calculateDerash(getAllJoinedNumbers(g).length, game.stake)

    profitSinceBotStarted -= win - g.realMoney
    alertAdmins(`${player.name} (${player.id}) (${gamesPlayed} games played) just won ${win}ETB.`)
  
    if (player.referral && game.stake) {
      var referrer = await User.findOne({id: player.referral})
      if (referrer) {
        var referralTransaction = new Transaction({amount: win*REFERRAL_PERCENTAGE, to: referrer, complete: true})
        
        await safeSendMessage(referrer.id, `Congratulations! ${player.name} won ${player.wonFirstGame ? "their first game" : "a game"} and you won ${referralTransaction.amount}ETB.`)
  
        referrer.balance += referralTransaction.amount
        player.wonFirstGame = true
        win *= 1-REFERRAL_PERCENTAGE
  
        await referrer.save()
        await referralTransaction.save()
      }
    }
    var transaction = new Transaction({amount: win, to: player, complete: true})
  
    player.balance += win;
    game.winner = player._id;
    await transaction.save()
    await player.save()  
    await safeSendMessage(player.id, `Congratulations, ${player.name}! You just won ${transaction.amount}ETB.`)
  }

  game.calledNumbers = g.calledNumbers.map(x => ''+x)
  
  await game.save()  

  var data = {
    calledNumbers: g.calledNumbers, 
    winnerName: await getWinnerName(game), 
    winnerBoard: getWinnerBoard(game), 
    winnerNumber: getWinnerNumber(game),
    winnerPatterns: checkWin(g.calledNumbers, getWinnerBoard(game)),
    bonus: undefined
  }
  g.b({over: {
    ...data,
    youWon: false
  }}, player._id)

  g.broadcast.find(s => s.authenticated == player._id)?.jsend({over:{
    ...data,
    youWon: true
  }})

  if (games.get(''+g.stake).botNumbers.length == 0) await startSpawningBots(games.get(''+g.stake))
  deleteGame(g)
}


const finishMaxGame = async (g) => {
  var game = await Game.findById(asId(g.id))
  if (g.joinedBotNumbers.length) {
    await selectWinner(
      game, 
      undefined, 
      g, 
      g.joinedBotNumbers[Math.floor(Math.random()*g.joinedBotNumbers.length)]
    )
  } else {
    await selectWinner(
      game, 
      game.players[Math.floor(Math.random()*game.players.length)], 
      g
    )
  }
}


async function getWinnerName(game) {
  if (game.botWon) return getFakeUsername(game)
  try {
    return (await User.findById(game.winner)).name
  } catch {
    return "Anonymous"
  }
}
function getWinnerNumber(game) {
  if (game.botWon) return game.numbers[game.numbers.length-1]
  return game.numbers[game.players.indexOf(game.winner)]
}
const getWinnerBoard = (game) => newCartela(game)[ getWinnerNumber(game) - 1 ]

const checkPattern = (called, cartela, pattern) =>  pattern.filter(cell => !called.includes(cartela[cell[0]][cell[1]])).length == 0
function checkWin(called, cartela) {
  if (!called.includes('*')) called.push('*')

  var patterns = [
    [[0,0], [0,4], [4,0], [4,4]] // corners
  ]
  var diag1 = []
  var diag2 = []
  for (var i = 0; i < 5; i++) {
    var col = []
    var row = []
    for (var j = 0; j < 5; j++) {
      col.push([i, j])
      row.push([j, i])
    }  
    patterns.push(col)
    patterns.push(row)
    diag1.push([i, i])
    diag2.push([4-i, i])
  }
  patterns.push(diag1)
  patterns.push(diag2)

  patterns = patterns.filter(
    p => checkPattern(called, cartela, p)
  )

  return patterns.length > 0 ? patterns : false
}

loadSettings()
main()
alertAdmins('Bot started!')
//respawnOpenGames()
bot.sendPhoto(ADMINS[0], "./new_profile_landscape.jpg", {
  caption: `Bot started!`
}).then(res => {
  BACKGROUND_IMAGE = res.photo[0].file_id
})