html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  background: radial-gradient(circle 540px at center, #92e0ff 40% , #4FC3F7 50%, #3a6ad2 90%);
}

@font-face {
  font-family: 'Lexend-Bold';
  src: url('fonts/Lexend-Bold.woff2') format('woff2');
}
.body {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: inline-flex;
}

.grid-container {
  display: grid;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 15px;
  padding-bottom: 15px;
  grid-template-columns: repeat(10, 1fr);
  gap: 1px;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.562);
  border-radius: 10px;
}

.grid-item {
  width: 1.8rem;
  height: 1.8rem;
  background: radial-gradient(circle 30px at center, #ffffff83 50% , #7878788f 90%);
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1em;
  color: #5d3c21;
  font-family: Inter;
  font-weight: 900;
  overflow: visible; 
  min-width: 10; 
}

.active-container {
  flex: 1 1 0;
  justify-content: space-between;
  align-items: center;
  gap: 5px;
  display: flex;
  padding-bottom: 5px;
}

.active-container-inner {
  flex: 1 1 0;
  flex-direction: column;
  background: white;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25) inset;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  height:42px;
  display: flex;
}
.active-container-inner.wallet{
  flex-grow: 1.5;
}

.small-grid-container {
  display: grid;
  grid-template-columns: repeat(5, auto);
  grid-template-rows: repeat(5, auto);
  gap: 2px;
  justify-content: center;
  align-items: center;
  margin-right: 20%;
  margin-left: 10%;
  margin-top: 10px;
}

.small-grid-item {
  width: 1.2rem;
  height: 1.2rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.75em;
  color: #464a54;
  font-family: Inter;
  font-weight: 700;
}

.lower-half {
  margin-top: 20px;
  display: flex;
  margin-bottom: 30px;
}
.inner-container {
  justify-content: center;
  align-items: center;
  gap: 20px;
  display: inline-flex;
  padding-top: 1px;
  flex-direction: column-reverse;
}

.refresh-button {
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 15px;
  padding-bottom: 15px;
  background: rgba(255, 255, 255, 0.608);
  box-shadow: 4px 15px 62px rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  gap: 5rem;
  display: flex;
  border: none;
  cursor: pointer;
}

.refresh-button:hover {
  transform: scale(1.05); /* Add scale effect on hover */
  transition: transform 0.2s ease; /* Add transition */
}

.refresh-button:active {
  transform: scale(0.95); /* Add scale effect on click */
}

.start-game-button {
  flex: 1 1 0;
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 10px;
  padding-bottom: 10px;
  background:rgba(255, 255, 255, 0.608);
  box-shadow: 4px 15px 62px rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  display: flex;
  border: none;
  cursor: pointer;
}

.start-game-button:hover {
  transform: scale(1.05); /* Add scale effect on hover */
  transition: transform 0.2s ease; /* Add transition */
}

.start-game-button:active {
  transform: scale(0.95); /* Add scale effect on click */
}

.top-text {
  color: #613c15; 
  font-size: 14px; 
  font-family: "Lexend-Bold", sans-serif;
  font-weight: 600; 
  word-wrap: break-word;
}

.refresh-text {
  font-family: "Lexend-Bold", sans-serif;
  color: #613c15; 
  text-align: center;
  font-size: 14px;
  font-weight: 800;
  transform: skew(-10deg, 0deg);
}
.start-button {
  font-family: "Lexend-Bold", sans-serif;
  color: #613c15; 
  text-align: center;
  font-size: 23px;
  font-weight: 800;
  transform: skew(-10deg, 0deg);
}
.copyright-text {
  text-align: center;
  color: #fffef5;
  font-size: 12px;
  font-family: SF Pro Text;
  font-weight: 400;
  word-wrap: break-word;
}

.signal-container {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: fit-content;
}

.signal-bar {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 10px;
  background-color: rgba(211, 211, 211, 0.667); /* Default color for all bars */
  border-radius: 20px;
  transition: background-color 0.4s ease; /* Smooth color transition */
}

/* Set fixed heights for the bars */
#bar1 {
  height: 8px;
}

#bar2 {
  height: 15px;
}

#bar3 {
  height: 20px;
}


/* Active states for bars, varying color but maintaining height */
.active-1 {
  background-color: rgb(255, 0, 0);
}

.active-2 {
  background-color: orange;
}

.active-3 {
  background-color: rgb(26, 255, 0);
}
