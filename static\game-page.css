html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  background: radial-gradient(circle 540px at center, #92e0ff 40% , #4FC3F7 50%, #3a6ad2 90%);
  display: flex;
  flex-direction: column;
}

@font-face {
  font-family: 'Lexend-Bold';
  src: url('fonts/Lexend-Bold.woff2') format('woff2');
}
.body {
  display: flex;
  flex-direction: column;
}

.container1 {
  height: auto;
  margin: 0;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 0px;
  padding-right: 0px;
  border-radius: 10px;
  align-items: flex-start;
  gap: 5px;
  display: flex;
  justify-content: space-between;
  width:100%;
  
}

.inner-container {
  display: inline-flex;
  flex: 1 1 0;
  flex-direction: column;
  width: 250px; /* Fixed width for each item */
  overflow: hidden; /* Clip the overflow */
  white-space: nowrap; /* Keep text in a single line */
  height: 44px;
  padding-top: 10px;
  padding-bottom: 10px;
  background: rgba(243, 243, 243, 0.678);
  border-radius: 10px;
  align-items: center;
}

.text {
  color: #613c15;
  font-size: 12px;
  font-family: "Lexend-Bold", sans-serif;
  font-weight: 800;
  word-wrap: break-word;
}

.amount {
  color: #613c15;
  font-size: 12px;
  font-family: SF Pro Text;
  font-weight: 700;
  word-wrap: break-word;
}

.buttons-container {
  flex-direction: column;
  /* Changed to column */
  justify-content: flex-start;
  align-items: center;
  display: inline-flex;
}

.buttons-inner-container {
  flex: 1 1 0;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 15px;
  padding-bottom: 15px;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
}

.button-container {
  justify-content: flex-center;
  align-items: center;
  gap: 20px;
  display: inline-flex;
}

.button {
  flex: 1 1 0;
  height: 44px;
  padding-left: 52px;
  padding-right: 52px;
  padding-top: 10px;
  padding-bottom: 10px;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  border: none;
  display: flex;
}

.bingo-button {
  margin-bottom: 2rem;
  height: 54px;
  background-color: #4bc72d;
  width: 100%;
}

.reset-button {
  /*background: linear-gradient(0deg, #229ED9 0%, #229ED9 100%),
        radial-gradient(59.56% 59.56% at 44.88% 67.14%, rgba(255, 255, 255, 0.41) 0%, rgba(255, 255, 255, 0) 70%, rgba(255, 255, 255, 0) 100%),
        radial-gradient(65.57% 52.41% at 244.23% 8.59%, rgba(0, 0, 0, 0.23) 0%, rgba(0, 0, 0, 0) 86%);*/
  background-image: linear-gradient(to bottom right, #229ed9 10%, #229ed9 70%);
}

.leave-button {
  background-image: linear-gradient(to bottom right, #e64646 10%, #e64646 70%);
  /*background: linear-gradient(0deg, #E64646 0%, #E64646 100%),
        radial-gradient(59.56% 59.56% at 44.88% 67.14%, rgba(255, 255, 255, 0.41) 0%, rgba(255, 255, 255, 0) 70%, rgba(255, 255, 255, 0) 100%),
        radial-gradient(65.57% 52.41% at 244.23% 8.59%, rgba(0, 0, 0, 0.23) 0%, rgba(0, 0, 0, 0) 86%);
      box-shadow: 4px 15px 62px rgba(0, 0, 0, 0.50);*/
}

.button-text {
  text-align: center;
  color: #fefded;
  font-size: 16px;
  font-family: SF Pro Text;
  font-weight: 600;
}

.bingo-button-text {
  font-family: "Lexend-Bold", sans-serif;
  color: #613c15; 
  text-align: center;
  font-size: 14px;
  font-weight: 800;
  transform: skew(-10deg, 0deg);
}
.text-button-bingo {
  font-family: "Lexend-Bold", sans-serif;
  color: #613c15; 
  text-align: center;
  font-size: 20px;
  font-weight: 800;
  transform: skew(-10deg, 0deg);
}

.grids-container {
  margin: 0.5rem;

  height: 70%;

  justify-content: center;
  align-items: flex-center;
  gap: 10px;
  display: inline-flex;
  flex-direction: row;
  /* Added column direction */
}

.bingo-grid {
  padding-top: 5px;
  padding-bottom: 2%;
  background: rgba(255, 255, 255, 0.562);
  border-radius: 10px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
  display: inline-flex;
}

.bingo-grid1 {
  padding: 5px;
  background: rgba(255, 255, 255, 0.562);
  border-radius: 10px;
  flex-direction: column;
  align-items: center;
  align-self: stretch;
  gap: 5px;
  display: inline-flex;
}

.color-square-container {
  justify-content: center;
  align-items: flex-start;
  gap: 5px;
  display: inline-flex;
}

.color-square-container1 {
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  display: inline-flex;
}

.color-square {
  width: 1.53rem;
  height: 1.53rem;
  border-radius: 5px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  display: inline-flex;
}

.color-square1 {
  width: 30px;
  height: 30px;
  border-radius: 20px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  display: inline-flex;
}

.color-square-B {
  background: #faa300;
}

.color-square-I {
  background: #5acd30;
}

.color-square-N {
  background: #4b91ca;
}

.color-square-G {
  background: #e64646;
}

.color-square-O {
  background: #7b499d;
}

.color-square-text {
  text-align: center;
  color: white;
  font-size: 12px;
  font-family: Inter;
  font-weight: 700;
  word-wrap: break-word;
}

.color-square-text1 {
  text-align: center;
  color: white;
  font-size: 18px;
  font-family: Inter;
  font-weight: 700;
  word-wrap: break-word;
}

.number-grid-container {
  display: grid;
  grid-template-columns: repeat(5, auto);
  grid-template-rows: repeat(5, auto);
  justify-content: center;
  align-items: center;
  margin-top: 2px;
}

.number-grid-item {
  width: 5px;
  height: 12px;
  padding: 10px;
  background: rgba(255, 250, 199, 0.35);
  border-radius: 5px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1px;
  display: inline-flex;
  margin-left: 5px;
  margin-right: 5px;
  margin-top: 0.205rem;
  margin-bottom: 0.205rem;
  font-size: 12px;
  /* Increased font size */
  color: #613c15;;
  /* Changed text color to black */
  font-family: serif;
  font-weight: 700;
  word-wrap: break-word;
}

.wrapper {
  flex: 1 1 0;
  align-self: stretch;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;
  display: inline-flex;
}

.inner-wrapper {
  align-self: stretch;
  height: 108px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
  display: flex;
}

.info-box {
  align-self: stretch;
  padding: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  justify-content: space-between;
  align-items: center;
  display: inline-flex;
}

.info-content {
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
  display: flex;
}

.info-icon {
  width: 16px;
  height: 16px;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 1px;
  padding-bottom: 1px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.icon {
  width: 10px;
  height: 14px;
  background: #464a54;
}

.info-text {
  text-align: center;
  color: #613c15;
  font-size: 15px;
  font-family: SF Pro Text;
  font-weight: 500;
  word-wrap: break-word;
}

.info-value {
  text-align: center;
  color: #613c15;
  font-size: 24px;
  font-family: SF Pro Text;
  font-weight: 700;
  word-wrap: break-word;
}

.call-grid {
  align-self: stretch;
  flex: 1 1 0;
  padding-top: 10px;
  padding-bottom: 10px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.call-box {
  margin-top: 5px;
  display: grid;
  grid-template-columns: repeat(5, auto);
  grid-template-rows: repeat(5, auto);
  justify-content: center;
  align-items: flex-center;
  gap: 4px;
}

.winner-call-box {
  margin-top: 8px;
  display: grid;
  grid-template-columns: repeat(5, auto);
  grid-template-rows: repeat(5, auto);
  justify-content: center;
  align-items: flex-center;
  gap: 4px;
}

.call-cell {
  width: 35px;
  height: 35px;
  padding: 3px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 5px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  display: inline-flex;

  text-align: center;
  color: #464a54;
  font-size: 18px;
  font-weight: 700;
  word-wrap: break-word;
}

.current-call {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60.72px;
  text-align: center;
  background: #4bc72d;
  box-shadow: 1.4457833766937256px 6px 22.409643173217773px rgba(0, 0, 0, 0.2);
  border-radius: 30px;
}

.current-call-span {
  text-align: center;
  color: white;
  font-size: 20px;
  font-family: SF Pro Text;
  font-weight: 700;
  word-wrap: break-word;
}

.last-5-info-box {
  padding: 10px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  justify-content: space-between;
  justify-content: center;
  align-items: center;
}

.last-5-call-grid-item {
  width: 30px;
  height: 30px;
  border-radius: 50px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 1%;
  display: inline-flex;
  margin-left: 3px;
  margin-right: 3px;
  font-size: 10px;
  /* Increased font size */
  color: #ffffff;
  /* Changed text color to black */
  font-family: SF Pro Text;
  font-weight: 500;
  word-wrap: break-word;
}

.inner-content {
  align-self: stretch;
  height: 149px;
  padding-top: 20px;
  padding-bottom: 20px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
  display: flex;
}

/* Bingo box styling */
.bingo-box {
  height: 10rem;
  padding-left: 20px;
  padding-right: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
  background: radial-gradient(circle 200px at center, #16e15a 0% , #199221 90%);
  border-radius: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5px;
  display: flex;
}

/* Text styling */
.bingo-text {
  text-align: center;
  color: #ffe7ce;
  font-size: 36px;
  font-family: "Lexend-Bold", sans-serif;
  font-weight: 700;
  padding-bottom: 5px;
  word-wrap: break-word;
}

.congrats-text {
  align-self: stretch;
  text-align: center;
  color: #ffe7ce;
  font-size: 20px;
  font-family: 'Lexend-Bold';
  font-weight: 500;
  word-wrap: break-word;
}

.bonus-text {
  align-self: stretch;
  text-align: center;
  color: white;
  font-size: 16px;
  font-family: SF Pro Text;
  font-weight: 500;
  word-wrap: break-word;
}

.winner-text {
  align-self: stretch;
  text-align: center;
  color: #613c15;
  font-size: 12px;
  font-family: "Lexend-Bold", sans-serif;
  font-weight: 700;
  word-wrap: break-word;
}

.signal-container {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 30px; /* Container height */
  width: fit-content;
}

.signal-bar {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  width: 10px;
  background-color: rgba(211, 211, 211, 0.667); /* Default color for all bars */
  border-radius: 20px;
  transition: background-color 0.4s ease; /* Smooth color transition */
}

/* Set fixed heights for the bars */
#bar1 {
  height: 8px;
}

#bar2 {
  height: 15px;
}

#bar3 {
  height: 20px;
}


/* Active states for bars, varying color but maintaining height */
.active-1 {
  background-color: rgb(255, 0, 0);
}

.active-2 {
  background-color: orange;
}

.active-3 {
  background-color: rgb(26, 255, 0);
}
