  <div class="modal text-center" id="bingoModal" tabindex="-1" aria-labelledby="bingoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="background: radial-gradient(circle 540px at center, #4dccff 40% , #4FC3F7 50%, #3a6ad2 90%);">
        <div class="inner-content text-center">
          <div class="bingo-box">
            <div class="bingo-text">BINGO!</div>
            <div class="congrats-text"></div>
            <div class="bonus-text"></div>
          </div>
        </div>
        
        <div class="modal-body ">
          <div class="bingo-grid1">
            <div class="color-square-container1">
              <div class="color-square1 color-square-B">
                <div class="color-square-text1">B</div>
              </div>
              <div class="color-square1 color-square-I">
                <div class="color-square-text1">I</div>
              </div>
              <div class="color-square1 color-square-N">
                <div class="color-square-text1">N</div>
              </div>
              <div class="color-square1 color-square-G">
                <div class="color-square-text1">G</div>
              </div>
              <div class="color-square1 color-square-O">
                <div class="color-square-text1">O</div>
              </div>
            </div>
            <div class="modal-body winner-call-box" id="winningBoard"></div>
            <div class="winner-text">Board number  <span id="bingoWinNum">-</span></div>
          </div>
        </div>
        
        <div class="text-center">
          <div class="buttons-inner-container">
            <button class="bingo-button button" data-bs-dismiss="modal">
              <div class="bingo-button-text">Play Again</div>
            </button>
          </div>
          <div
            style="text-align: center; color: #FFFEF5; font-size: 12px; font-family: SF Pro Text; font-weight: 400; word-wrap: break-word">
            © Blue Bingo 2024</div>
        <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> -->
        </div>
    </div>
    </div>
  </div>

  <div class="container-fluid">
    <div class="row">
        <div class="col">
          <div class="body">
            <div class="container1 mt-1">
              <div class="inner-container">
                <div class="text">WIN</div>
                
                  <div class="amount"><span id="derash">{{{derash}}}</span></div>
                
              </div>
              <!--
              <div class="inner-container">
                <div class="text">BONUS</div>
                
                <div id="bonus" class="amount"><span>-</span></div>
                
              </div>-->
              <div class="inner-container">
                <div class="text">PLAYERS</div>
                
                  <div class="amount"><span id="totalPlayers">{{{numPlayers}}}</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">BET</div>
                <div class="amount"><span>{{{stake}}}</span></div>
              </div>
              <div class="inner-container">
                <div class="text">CALL</div>
                <div class="amount"><span id="totalCall">0</span></div>
              </div>
              <div class="inner-container">
                 <div class="signal-container">
                    <div id="bar1" class="signal-bar"></div>
                    <div id="bar2" class="signal-bar"></div>
                    <div id="bar3" class="signal-bar"></div>
              </div>
              </div>
            </div>

            <div class="grids-container">
              <div class="bingo-grid">
                <div class="color-square-container">
                  <div class="color-square color-square-B">
                    <div class="color-square-text">B</div>
                  </div>
                  <div class="color-square color-square-I">
                    <div class="color-square-text">I</div>
                  </div>
                  <div class="color-square color-square-N">
                    <div class="color-square-text">N</div>
                  </div>
                  <div class="color-square color-square-G">
                    <div class="color-square-text">G</div>
                  </div>
                  <div class="color-square color-square-O">
                    <div class="color-square-text">O</div>
                  </div>
                </div>
                <div class="number-grid-container">
                </div>
              </div>
              <div class="wrapper">
                <div class="inner-wrapper">
                  <div class="info-box">
                    <div class="info-content">
                      <div class="info-text">Count Down</div>
                    </div>
                    
                      <div class="info-value"><span id="countdown">WAIT</span></div>
                    
                  </div>
                  <div
                    style="align-self: stretch; height: 51px; padding-left: 15px; padding-right: 15px; padding-top: 5px; padding-bottom: 5px; background: radial-gradient(circle 200px at center, #095674bf 40% , #5390acd2 70%);; border-radius: 10px; justify-content: space-between; align-items: center; display: inline-flex">
                    <div
                      style="text-align: center; color: white; font-size: 15px; font-family: SF Pro Text; font-weight: 700; word-wrap: break-word ">
                      Current Call
                    </div>
                    <div>
                      <div id="currentCallColor" class="current-call">
                        <span id="currentCall" class="current-call-span">-</span>
                      </div>
                    </div>
                      
                  </div>
                  <div id="lastNumbers" class="last-5-info-box">
                    <span class="last-5-call-grid-item">-</span>
                  </div>
                  <div class="bingo-grid1">
                    <div class="color-square-container1">
                      <div class="color-square1 color-square-B">
                        <div class="color-square-text1">B</div>
                      </div>
                      <div class="color-square1 color-square-I">
                        <div class="color-square-text1">I</div>
                      </div>
                      <div class="color-square1 color-square-N">
                        <div class="color-square-text1">N</div>
                      </div>
                      <div class="color-square1 color-square-G">
                        <div class="color-square-text1">G</div>
                      </div>
                      <div class="color-square1 color-square-O">
                        <div class="color-square-text1">O</div>
                      </div>
                    </div>
                    <div class="call-box"></div>
                    <div class="winner-text">BOARD NUMBER <span id="winNum">-</span></div>
                  </div>
                </div>

              </div>

            </div>
            <div class="buttons-container">
              <div class="buttons-inner-container">
                <button id="bingoButton" class="bingo-button button" onclick="submitBingoForm()">
                  <div class="text-button-bingo">BINGO!</div>
                </button>
                <div class="button-container">
                  <button class="reset-button button" onclick="refreshPage()">
                    <div class="bingo-button-text">Refresh</div>
                  </button>
                  <button class="leave-button button" onclick="leaveBingoForm()">
                    <div class="bingo-button-text">Leave</div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>

  <!-- Add a container for the notifications -->
  <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
    <div class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
          <div class="toast-body">
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  </div>

<style>
  .highlight-win {
    background-color: green;
    color: white; /* Adjust text color for better contrast */
  }

</style>

<script id="socket_url" type="application/json">{{{socketUrl}}}</script>
<script id="stake" type="application/json">{{{stake}}}</script>
<script id="gameId" type="application/json">{{{gameId}}}</script>


<script>
    var socket_url = JSON.parse(document.getElementById('socket_url').textContent);
    var stake = JSON.parse(document.getElementById('stake').textContent)
    var gameId = JSON.parse(document.getElementById('gameId').textContent);
    var selectedNumber
    var calledNumbers = []
    let socket;
    let derash = 0

    const OTHER_SELECTED = '#FF6701'

    Telegram.WebApp.ready();
    function newSocket() {
      socket = new WebSocket(socket_url);
      socket.addEventListener("open", (ev) => {
          socket.send(JSON.stringify({
              auth: Telegram.WebApp.initData,
              page: location.pathname,
              stake,
              gameId
          }))
      });

      socket.addEventListener("message", (ev) => {
          const data = JSON.parse(ev.data);
          console.log(data)
          if(data.init) {
            document.getElementById('totalPlayers').textContent = data.numPlayers
            //document.getElementById('game_number').textContent = data.playerName
            document.getElementById('winNum').textContent = data.selectedNumber
            selectedNumber = data.selectedNumber
            setupCallBox(data.cartelaBoard)
            calledNumbers = data.calledNumbers
            setupCalledNumbers()
            if (data.winnerName) displayWinner(data.winnerName, data.winnerNumber, data.winnerBoard, data.winnerPatterns, data.calledNumbers)
          } else if(data.countdown) {
            document.getElementById('countdown').textContent = (calledNumbers && calledNumbers.length ? "Started" : data.countdown);
          } else if (data.derash) {
            derash = data.derash
            document.getElementById('derash').textContent = data.derash;
          } else if (data.totalPlayers) {
            document.getElementById('totalPlayers').textContent = data.totalPlayers;
          } else if (data.bonus){
            document.getElementById('bonus').textContent = data.bonus;
        } else if (data.derash) {
          derash = data.derash
          document.getElementById('derash').textContent = data.derash;
        } else if (data.totalPlayers) {
          document.getElementById('totalPlayers').textContent = data.totalPlayers;
        } else if (data.over) {
            document.getElementById('totalCall').textContent = document.getElementById('currentCall').textContent;
            localStorage.clear();
            let winnerBoard = data.over.winnerBoard;
            let winnerNumber = data.over.winnerNumber
            let winnerCalledNumbers = data.over.calledNumbers
            let winnerPatterns = data.over.winnerPatterns;
            let winnerName = data.over.winnerName
            let youWon = data.over.youWon
            let bonus = data.over.bonus
          
            const winnerText = document.querySelector('.congrats-text');
            
            displayWinner(winnerName, winnerNumber, winnerBoard, winnerPatterns, winnerCalledNumbers)
            
            function nodeScriptClone(node){
              var script  = document.createElement("script");
              script.text = node.innerHTML;

              var i = -1, attrs = node.attributes, attr;
              while (++i < attrs.length) {                                    
                script.setAttribute( (attr = attrs[i]).name, attr.value );
              }
              return script;
            }

            if (youWon){
              winnerText.innerHTML = `You have won ${derash} ETB`;
              winnerText.style.color = 'green';
              const bonusText = document.querySelector('.bonus-text');
              bonusText.innerHTML = `<script async src="https://telegram.org/js/telegram-widget.js?22" data-telegram-share-url="${location.href.split("#")[0]}" data-comment="I just won ${derash}ETB on Blue Bingo! Join me at https://t.me/bluebingobot?start=${Telegram.WebApp.initDataUnsafe.user.id}"><`+`/script>`
              bonusText.replaceChild(nodeScriptClone(bonusText.children[bonusText.children.length-1]), bonusText.children[bonusText.children.length-1]);
              if (bonus){
                const bonusText = document.querySelector('.bonus-text');
                bonusText.innerHTML = "You have won a " + bonus + "ETB bonus";
                bonusText.style.color = 'green';
              }
            }else{
              const w = document.createElement('span')
              w.classList.add('badge', 'bg-success')
              w.textContent = winnerName
              winnerText.innerHTML = "";
              winnerText.appendChild(w)
              winnerText.innerHTML += ` has won!`;
              if (bonus){
                const bonusText = document.querySelector('.bonus-text');
                bonusText.innerHTML = "";
                bonusText.appendChild(w.cloneNode())
                bonusText.innerHTML += " has won!";
              }
            }
          } else if (data.number) {
            calledNumbers.push(data.number)
            document.getElementById('currentCall').textContent = toLetter(data.number) + "-" + data.number;
            setColor(document.getElementById('currentCallColor'), toLetter(data.number))
            updateCallBox(toLetter(data.number), data.number);
            updateLastNumbers(calledNumbers.slice(-5));
          } else if (data.notify || data.error){
              const toastEl = document.querySelector('.toast');
              const toast = new bootstrap.Toast(toastEl);
              const toastBody = toastEl.querySelector('.toast-body');
              toastBody.innerText = data.notify || data.error;
              toast.show();
          } else if (data.redirect) {
              localStorage.setItem('selectedItems', '{}');
              window.location.href = data.redirect
          } else if (data.kick) {
            localStorage.clear();
            const toastEl = document.querySelector('.toast');
            const toast = new bootstrap.Toast(toastEl);
            const toastBody = toastEl.querySelector('.toast-body');
            toastBody.innerText = "Removed. Clicked bingo without pattern match";
            toast.show(); 
            setTimeout(function() {
                window.location.href = "/choose-board?stake=" + stake;
            }, 3000);
          }
      });

      socket.addEventListener("close", () => setTimeout(newSocket, 1000));
      socket.onerror = () => setTimeout(newSocket, 1000);
  }

  function displayWinner(winnerName, winnerNumber, winnerBoard, winnerPatterns, winnerCalledNumbers) {
    const winnerText = document.querySelector('.congrats-text');
    const w = document.createElement('span')
    w.classList.add('badge', 'bg-success')
    w.textContent = winnerName
    winnerText.innerHTML = "";
    winnerText.appendChild(w)
    winnerText.innerHTML += ` has won!`;

    const winnerCallBox = document.querySelector('.winner-call-box');
    winnerCallBox.textContent = '';
    for (let i = 0; i < 5; i++) {
      for (let j = 0; j < 5; j++) {
        const callCell = document.createElement('div');
        callCell.classList.add('call-cell');
        callCell.textContent = winnerBoard[i][j];

        if (winnerCalledNumbers.includes(winnerBoard[i][j]) || winnerCalledNumbers.includes(''+winnerBoard[i][j])) {
          callCell.style.backgroundColor = OTHER_SELECTED;
        } 

        winnerPatterns.forEach(p => p.forEach(cell => {
          if (cell[0] == i && cell[1] == j) {
            callCell.style.backgroundColor = "green";
            callCell.classList.add('highlight-win');
          }
        }))

        winnerCallBox.appendChild(callCell);
      }
    }
    document.getElementById('winNum').textContent = winnerNumber
    document.getElementById('bingoWinNum').textContent = winnerNumber
    var myModal = new bootstrap.Modal(document.getElementById('bingoModal'));
    myModal.show();

    document.getElementById('totalCall').textContent = winnerCalledNumbers.length;
    updateLastNumbers(winnerCalledNumbers.slice(-5));
  }

  newSocket()

  const setColor = (x, col) => {
    ["B", "I", "N", "G", "O"].forEach(c => {
      if (c == col) x.classList.add('color-square-'+c) 
      else x.classList.remove('color-square-'+c)
    })
  }

  const updateLastNumbers = (lastNumbers) => {
    const lastNumbersDisplay = document.getElementById('lastNumbers');
    lastNumbersDisplay.textContent = '';
    for (let i = lastNumbers.length - 1; i >= 0; i--) {
        var n = {letter: toLetter(lastNumbers[i]), number: lastNumbers[i], color: '', textColor: ''}
        const span = document.createElement('span');
        span.textContent = n.letter + "-" + n.number;
        span.classList.add('last-5-call-grid-item');
        setColor(span, n.letter)
        span.style.backgroundColor = n.color;
        span.style.color = n.textColor; 
        lastNumbersDisplay.appendChild(span);
    }
  }

  // generate 75grid
  const numberGridContainer = document.querySelector('.number-grid-container');
  for (let i = 0; i < 15; i++) {
    for (let j = 0; j < 5; j++) {
        const value = i + j * 15 + 1;
        const numberGridItem = document.createElement('div');
        numberGridItem.classList.add('number-grid-item');
        numberGridItem.textContent = value;
        numberGridContainer.appendChild(numberGridItem);
    }
  }

  function setupCallBox(cartelaNumbers) {
    // generate bingo grid board
    const callBox = document.querySelector('.call-box');
    callBox.innerHTML = ''
    for (let i = 0; i < 5; i++) {
      for (let j = 0; j < 5; j++) {
        const callCell = document.createElement('div');
        callCell.classList.add('call-cell');
        callCell.textContent = cartelaNumbers[i][j];
        if (callCell.textContent === "*"){
          callCell.style.backgroundColor = 'green';
        }
        callBox.appendChild(callCell);
      }
    }
    // get bingo board value from local storage
    const gridItems = document.querySelectorAll('.call-cell');
    const storedSelectedItems = localStorage.getItem('selectedItems');
    let selectedItems = [];
    let clicked = null;
    if (storedSelectedItems) {
      selectedItems = JSON.parse(storedSelectedItems)[gameId] || [];
      
      selectedItems.forEach(localItemId => {
        gridItems.forEach(itemId => {
          if (localItemId === itemId.textContent){
            clicked = itemId.dataset.clicked = 'true';
            itemId.style.backgroundColor = 'green';
          }
        });
      });
    }

    function saveSelectedItems(i) {
      var storedItems = JSON.parse(localStorage.getItem('selectedItems') || '{}')
      storedItems[gameId] = selectedItems
      localStorage.setItem('selectedItems', JSON.stringify(storedItems))
    }

    //bingo grid board click update
    gridItems.forEach(item => {
      item.addEventListener('click', function selectGridItem(event) {
        const selectedItem = event.target;
        clicked = selectedItem.dataset.clicked === 'true';
        if (selectedItem.textContent === "*"){

        }else if (!clicked) {
          selectedItem.style.backgroundColor = 'green'; // Change color as needed
          selectedItem.dataset.clicked = 'true';

          selectedItems.push(selectedItem.textContent);
          saveSelectedItems()
        }else{
          item.style.backgroundColor = '';
          item.dataset.clicked = 'false';

          const itemIndex = selectedItems.indexOf(selectedItem.textContent);
          selectedItems.splice(itemIndex, 1);
          saveSelectedItems()
        }
      });
    });
  }


  //generate 75grid call box on page load
  function setupCalledNumbers() {
    const numberGridItems = document.querySelectorAll('.number-grid-item');
    if (calledNumbers){
      calledNumbers.forEach(callNum => {
        numberGridItems.forEach(item => {
          if (item.textContent.toString() === callNum.toString()) {
              item.style.backgroundColor = '#FF6701';
          }
        });
      });
    }
  }
  
  // Function to update numbers in 75grid call box and apply animation
  function updateCallBox(letter, number) {
    document.getElementById('totalCall').innerHTML = ''+calledNumbers.length;
    const numberGridItems = document.querySelectorAll('.number-grid-item');
    numberGridItems.forEach(item => {
      if (item.textContent.toString() === number.toString()) {
          item.style.backgroundColor = 'green';
          item.classList.add('blink-animation');
      } else if (item.style.backgroundColor === 'green') {
          item.style.backgroundColor = '#FF6701';
          item.classList.remove('blink-animation');
      } 
    });


    var cheat = false
    if (cheat) {
      // get bingo board value from local storage
      const gridItems = document.querySelectorAll('.call-cell');
        
      gridItems.forEach(itemId => {
        if (number.toString() === itemId.textContent){
          clicked = itemId.dataset.clicked = 'true';
          itemId.style.backgroundColor = 'green';
        }
      });
    }
  }

  //socket reconnect function
  function reconnectWebSocket() {
    if (!socket || socket.readyState === WebSocket.CLOSED) {
        console.log("Attempting to reconnect WebSocket...");
        try {
            socket = new WebSocket(socket_url);
        } catch (error) {
            console.error("Error occurred during WebSocket reconnect:", error);
            // Handle reconnect error (e.g., retry with backoff strategy)
        }
    }
  }

  const toLetter = (n) => (n ? "BINGO"[Math.floor((n-1)/15)] : "N")

  // game leave
  function leaveBingoForm(){
    localStorage.setItem('selectedItems','{}');
    window.location.href = "/choose-board?stake=" + stake;
  }

  // refresh the page
  function refreshPage() {
      location.reload();
  }

  // bingo button clicked
  function submitBingoForm() {
    event.preventDefault();
    var bingoButton = document.getElementById("bingoButton");
    bingoButton.disabled = true;

    // Re-enable the button after 3 seconds
    setTimeout(function() {
      bingoButton.disabled = false;
    }, 3000);

    socket.send(JSON.stringify({
      bingo: true,
      gameId,
      selectedNumber
    }))
  }

  var myModal = new bootstrap.Modal(document.getElementById('bingoModal'));
  myModal._element.addEventListener('hidden.bs.modal', function (event) {
      window.location.href = "/choose-board?stake=" + stake;
  });
</script>



<script id="signal-bars">

function checkNetworkConnectivity(url) {
    const start = Date.now(); // Get the current timestamp before the request
    fetch(url)
        .then(() => {
            const end = Date.now(); // Get the timestamp after the response
            const responseTime = end - start; // Calculate the response time
            
            // Update the signal bars based on the response time
            updateSignalBars(responseTime);
        })
        .catch((error) => {
          updateSignalBars(null);

          console.log("Error:", error);
});
}

// Function to update the signal bars based on the response time
function updateSignalBars(responseTime) {
    const bars = document.querySelectorAll('.signal-bar');

    // Clear all active classes (reset to lightgray)
    bars.forEach(bar => bar.className = 'signal-bar');

    if (responseTime === null) {
        // If no response (server unreachable), leave all bars gray
        return;
    }

    // Determine signal strength based on response time
    if (responseTime < 200) {
        activateBars(3);
    } else if (responseTime < 400) {
        activateBars(2);
    } else {
        activateBars(1); // Weakest signal
    }
}

// Function to activate a number of signal bars based on strength
function activateBars(strength) {
    for (let i = 1; i <= strength; i++) {
        const bar = document.getElementById('bar' + i);
        bar.classList.add(`active-${i}`);
    }
}

// Call the checkNetworkConnectivity function every 5 seconds
setInterval(() => {
    checkNetworkConnectivity('https://bluebingoet.com/ping'); // Replace with your server URL
}, 5000);

</script>
