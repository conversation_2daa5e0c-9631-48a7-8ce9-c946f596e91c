
    <div class="container">
            <div class="active-container mt-1">
                <div class="active-container-inner">
                    <div class="top-text">STAKE</div>
                    
                    <div id='stake_vis' class="top-text"> {{stake}} </div>
                </div>
                <div class="active-container-inner wallet">
                    <div class="top-text"> WALLET </div>
                    <div id='wallet_vis' class="top-text">
                        0 </div>
                </div>
                 <div class="active-container-inner wallet">
                    <div class="top-text"> BONUS </div>
                    <div id='bonus_vis' class="top-text">
                        0 </div>
                </div>
                <div class="active-container-inner">
                  <div class="top-text"> GAME </div>
                    <div id='game_vis' class="top-text">
                        - </div>
                </div>
            </div>
            <div class="container mt-2">
                <div class="grid-container"></div>
                    <div class="lower-half">
                        <div class="small-grid-container"></div>
                        <div class="inner-container">
                            <button class="refresh-button" onclick="refreshPage()">
                                <div class="refresh-text">REFRESH</div>
                            </button>
                            <button id="game_start" class="start-game-button" onclick="handlePlayClick()">
                                <div class="start-button">START!</div>
                            </button>
                        </div>
                    </div>
                     <div class="copyright-text">© Blue  Bingo 2024</div>
                </div>
            </div>
    </div>

     <!-- Add a container for the notifications -->
     <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
        
        <div class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
    <script id="socket_url" type="application/json">{{{socketUrl}}}</script>
    <script id="stake" type="application/json">{{{stake}}}</script>
    <script id="cartela" type="application/json">{{{cartela}}}</script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        var socket_url = JSON.parse(document.getElementById('socket_url').textContent);
        var stake = JSON.parse(document.getElementById('stake').textContent)
        var cartela = JSON.parse(document.getElementById('cartela').textContent);
        
        let socket;

        Telegram.WebApp.ready();

        function newSocket() {
            console.log('ws')
            socket = new WebSocket(socket_url);
            socket.addEventListener("open", (ev) => {
                socket.send(JSON.stringify({
                    auth: Telegram.WebApp.initData,
                    page: location.pathname ,
                    stake
                }))
            });

            socket.addEventListener("message", (ev) => {
                var startButton = document.getElementById("game_start");
                const data = JSON.parse(ev.data);
                console.log(data)
                if(data.balance != undefined) {
                    document.getElementById('wallet_vis').textContent = ''+ Math.floor(data.balance)
                } else if(data.bonusBalance != undefined) {
                    if (data.bonusBalance) document.getElementById('bonus_vis').textContent = '' + Math.floor(data.bonusBalance)
                } else if (data.activeGames != undefined) {
                    document.getElementById('game_vis').textContent = data.activeGames
                } else if (data.cartela) {
                    setCartela(data.cartela)    
                } else if (data.notify || data.error){
                    const toastEl = document.querySelector('.toast');
                    const toast = new bootstrap.Toast(toastEl);
                    const toastBody = toastEl.querySelector('.toast-body');
                    toastBody.innerText = data.notify || data.error;
                    toast.show();
                    startButton.disabled = false;
                } else if (data.selected) {
                    selectNumber(data.selected)
                } else if (data.joined) {
                    selectNumber(data.joined, undefined, true)
                } else if (data.unselected) {
                    unselectNumber(data.unselected)
                } else if (data.startGame) {
                    window.location.href = "/bingo?game" + data.startGame;
                } else if (data.redirect) {
                    window.location.href = data.redirect
                } else if (data.allSelected) {
                    createGrid()
                    const gridItems = document.querySelectorAll('.grid-item');
                    for (var i = 0; i < data.allSelected.length; i++) {
                        if (data.allSelected[i]) {
                            var boardNum = data.allSelected[i]-1;
                            gridItems[boardNum].style.backgroundColor = OTHER_SELECTED;                            
                        }
                    }
                } else if (data.allJoined) {
                    const gridItems = document.querySelectorAll('.grid-item');
                    for (var i = 0; i < data.allJoined.length; i++) {
                        if (data.allJoined[i]) {
                            var boardNum = data.allJoined[i]-1;
                            gridItems[boardNum].style.backgroundColor = OTHER_JOINED;                            
                        }
                    }
                } else if (data.alreadyStarted) {
                    localStorage.clear();
                    const toastEl = document.querySelector('.toast');
                    const toast = new bootstrap.Toast(toastEl);
                    const toastBody = toastEl.querySelector('.toast-body');
                    toastBody.innerText = "Game already started. Wait until it is finished.";
                    toast.show();
                    setTimeout(function() {
                        window.location.href = "/choose-board?stake=" + stake;
                    }, 3000);
                }
            });

            socket.addEventListener("close", () => setTimeout(newSocket, 1000));
            socket.onerror = () => setTimeout(newSocket, 1000);
        }
    
        newSocket()

        var selectedBoardNumber = null;

        function unselectNumber(n) {
            const gridItems = document.querySelectorAll('.grid-item');
            gridItems.forEach(item => {
                if (item.textContent == ''+n) {
                    item.style.backgroundColor = ''
                    item.dataset.clicked = 'false';
                }
            })
        }
        function selectNumber(n, me, joined) {
            const gridItems = document.querySelectorAll('.grid-item');
            gridItems.forEach(item => {
                if (item.textContent == ''+n) {
                    item.style.backgroundColor = me ? 'green' : (joined ? OTHER_JOINED : OTHER_SELECTED)
                    item.dataset.clicked = 'true';
                }
            })
        }

        function sendSelect(n) {
            socket.send(JSON.stringify({
                selected:n
            }))
        }
        function sendUnselect(n) {
            if (''+selectedBoardNumber != ''+n) return
            unselectNumber(n)
            socket.send(JSON.stringify({
                unselected:n
            }))
        }

        const OTHER_SELECTED = '#FF6701'
        const OTHER_JOINED = '#0127FF'

        //Populate the board grid
        var clickedCell = null;
        function createGrid() {
            const gridContainer = document.querySelector('.grid-container');
            gridContainer.textContent = '';
            for (let i = 1; i <= 100; i++) {
                const gridItem = document.createElement('div');
                gridItem.classList.add('grid-item');
                gridItem.textContent = i;
                gridContainer.appendChild(gridItem);
            }
            const gridItems = document.querySelectorAll('.grid-item');
            gridItems.forEach(item => {
                item.addEventListener('click', function selectGridItem(event) {
                    const selectedItem = event.target;
                    const clicked = selectedItem.dataset.clicked === 'true';
                    unselectNumber(selectedBoardNumber)
                    if (!clicked) {
                        const itemColor = window.getComputedStyle(item).getPropertyValue('background-color');
                        if (itemColor !== 'rgb(39, 187, 115)' && itemColor !== OTHER_SELECTED && itemColor !== OTHER_JOINED){
                            selectedItem.style.backgroundColor = 'green'; // Change color as needed
                            selectedItem.dataset.clicked = 'true';
                            selectedBoardNumber = parseInt(selectedItem.innerHTML)
                            sendSelect(parseInt(selectedItem.innerHTML))
                            selectNumber(parseInt(selectedItem.innerHTML), true)
                        }
                        
                    } else {
                        sendUnselect(parseInt(selectedItem.innerHTML))
                    }
                    generateSmallBoard(parseInt(selectedItem.textContent));
                });
            });
        }

        function setCartela(c) {
            cartela = c;
            createGrid()
        }

        // Generate small board data
        function generateSmallBoard(startingNumber) {
            const smallGridContainer = document.querySelector('.small-grid-container');
            smallGridContainer.innerHTML = '';
            for (let i = 0; i < 5; i++) {
                for (let j = 0; j < 5; j++){
                    const gridItem = document.createElement('div');
                    gridItem.classList.add('small-grid-item');
                    gridItem.textContent = cartela[startingNumber-1][i][j];
                    smallGridContainer.appendChild(gridItem);
                }
                
            }
        }


        // Join button click
        function handlePlayClick() {
            event.preventDefault();
            var startButton = document.getElementById("game_start");
            startButton.disabled = true;

            socket.send(JSON.stringify({
                join: stake,
            }));

            setTimeout(() => startButton.disabled=false, 3000);
        }

        // Refrash tge page
        function refreshPage() {
            try {
                socket.close();
            } catch (error) {}
            setTimeout(() => location.reload(), 500);
        }
        
    </script>