  <div class="modal text-center" id="bingoModal" tabindex="-1" aria-labelledby="bingoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="background:#AD88C6;">
        <div class="inner-content text-center">
          <div class="bingo-box">
            <div class="bingo-text">BINGO!</div>
            <div class="congrats-text"></div>
            <div class="bonus-text"></div>
          </div>
        </div>
        
        <div class="modal-body ">
          <div class="bingo-grid1">
            <div class="color-square-container1">
              <div class="color-square1 color-square-B">
                <div class="color-square-text1">B</div>
              </div>
              <div class="color-square1 color-square-I">
                <div class="color-square-text1">I</div>
              </div>
              <div class="color-square1 color-square-N">
                <div class="color-square-text1">N</div>
              </div>
              <div class="color-square1 color-square-G">
                <div class="color-square-text1">G</div>
              </div>
              <div class="color-square1 color-square-O">
                <div class="color-square-text1">O</div>
              </div>
            </div>
            <div class="modal-body winner-call-box" id="winningBoard"></div>
            <div class="winner-text">Board number  <span id="winNum">-</span></div>
          </div>
        </div>
        
        <div class="text-center">
          <div class="buttons-inner-container">
            <button class="bingo-button button" data-bs-dismiss="modal">
              <div class="bingo-button-text">Play Again</div>
            </button>
          </div>
          <div
            style="text-align: center; color: #FFFEF5; font-size: 12px; font-family: SF Pro Text; font-weight: 400; word-wrap: break-word">
            © Blue Bingo 2024</div>
        <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> -->
        </div>
    </div>
    </div>
  </div>

  <div class="container-fluid">
    <div class="row">
        <div class="col">
          <div class="body">
            <div class="container1 mt-1">
              <div class="inner-container">
                <div class="text">Game</div>
                  <div class="amount"><span>60174</span></div>
              </div>
              <div class="inner-container">
                <div class="text">Derash</div>
                
                  <div class="amount"><span id="derash">232</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">Bonus</div>
                
                <div id="bonus" class="amount"><span>-</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">Players</div>
                
                  <div class="amount"><span id="totalPlayers">29</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">Bet</div>
                <div class="amount"><span>10</span></div>
              </div>
              <div class="inner-container">
                <div class="text">call</div>
                <div class="amount"><span id="totalCall">0</span></div>
              </div>
            </div>

            <div class="grids-container">
              <div class="bingo-grid">
                <div class="color-square-container">
                  <div class="color-square color-square-B">
                    <div class="color-square-text">B</div>
                  </div>
                  <div class="color-square color-square-I">
                    <div class="color-square-text">I</div>
                  </div>
                  <div class="color-square color-square-N">
                    <div class="color-square-text">N</div>
                  </div>
                  <div class="color-square color-square-G">
                    <div class="color-square-text">G</div>
                  </div>
                  <div class="color-square color-square-O">
                    <div class="color-square-text">O</div>
                  </div>
                </div>
                <div class="number-grid-container">
                </div>
              </div>
              <div class="wrapper">
                <div class="inner-wrapper">
                  <div class="info-box">
                    <div class="info-content">
                      <div class="info-text">Count Down</div>
                    </div>
                    
                      <div class="info-value"><span id="countdown">Wait</span></div>
                    
                  </div>
                  <div
                    style="align-self: stretch; height: 51px; padding-left: 15px; padding-right: 15px; padding-top: 5px; padding-bottom: 5px; background: #7B499D; border-radius: 20px; justify-content: space-between; align-items: center; display: inline-flex">
                    <div
                      style="text-align: center; color: white; font-size: 15px; font-family: SF Pro Text; font-weight: 700; word-wrap: break-word ">
                      Current Call
                    </div>
                    <div>
                      <div id="currentCallColor" class="current-call">
                        <span id="currentCall" class="current-call-span">-</span>
                      </div>
                    </div>
                      
                  </div>
                  <div id="lastNumbers" class="last-5-info-box">
                    <span class="last-5-call-grid-item">-</span>
                  </div>
                  <div class="bingo-grid1">
                    <div class="color-square-container1">
                      <div class="color-square1 color-square-B">
                        <div class="color-square-text1">B</div>
                      </div>
                      <div class="color-square1 color-square-I">
                        <div class="color-square-text1">I</div>
                      </div>
                      <div class="color-square1 color-square-N">
                        <div class="color-square-text1">N</div>
                      </div>
                      <div class="color-square1 color-square-G">
                        <div class="color-square-text1">G</div>
                      </div>
                      <div class="color-square1 color-square-O">
                        <div class="color-square-text1">O</div>
                      </div>
                    </div>
                    <div class="call-box"></div>
                    <div class="winner-text">Board number  <span id="winNum">54</span></div>
                  </div>
                </div>

              </div>

            </div>
            <div class="buttons-container">
              <div class="buttons-inner-container">
                <button id="bingoButton" class="bingo-button button" onclick="submitBingoForm()">
                  <div class="bingo-button-text">BINGO!</div>
                </button>
                <div class="button-container">
                  <button class="reset-button button" onclick="refreshPage()">
                    <div class="bingo-button-text">Refresh</div>
                  </button>
                  <button class="leave-button button" onclick="leaveBingoForm()">
                    <div class="bingo-button-text">Leave</div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>

  <!-- Add a container for the notifications -->
  <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
    <div class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
          <div class="toast-body">
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  </div

<script id="token" type="application/json">"id=408231241\u0026stake=10\u0026auth_date=1723365904\u0026hash=adfc57be3b3db9bea96fd28b6084633e0ede72b6a0e10508912916758df7488d"</script>
<script id="socket_url" type="application/json">"wss://addisbingobot.com/ws/game/game60174/?user_id=408231241"</script>
<script id="gameData" type="application/json">{"board": [[1, 26, 34, 60, 61], [6, 18, 35, 52, 66], [4, 24, "*", 50, 69], [15, 29, 32, 48, 63], [7, 25, 45, 53, 72]], "lastCalledNumber": [{"text": "white", "color": "#FAA300", "letter": "B", "number": "14"}, {"text": "white", "color": "#E64646", "letter": "G", "number": "58"}, {"text": "white", "color": "#7B499D", "letter": "O", "number": "68"}]}</script>
<script id="called_number" type="application/json">[[{"text": "white", "color": "#FAA300", "letter": "B", "number": "14"}, {"text": "white", "color": "#E64646", "letter": "G", "number": "58"}, {"text": "white", "color": "#7B499D", "letter": "O", "number": "68"}], [14, 58, 68]]</script>


<script>
    var token = JSON.parse(document.getElementById('token').textContent);
    var socket_url = JSON.parse(document.getElementById('socket_url').textContent);
    var gameData = JSON.parse(document.getElementById('gameData').textContent);
    var called_number = JSON.parse(document.getElementById('called_number').textContent);
    let socket;
 
    Telegram.WebApp.ready();
//   const socket = new WebSocket(socket_url + "/ws/game/" + game_room + "/?user_id=" + user_id);
    socket = new WebSocket(socket_url);
    socket.onmessage = function(event) {


















      
      const data = JSON.parse(event.data);
      if (data.gameState === "countdown"){
          document.getElementById('countdown').textContent = data.countdown;
      }
      else if (data.gameState === "derash"){
          document.getElementById('derash').textContent = data.derash;
          document.getElementById('totalPlayers').textContent = data.totalPlayers;
          if (data.bonus){
            document.getElementById('bonus').textContent = data.bonus;
          }
      }else{
          document.getElementById('currentCall').textContent = data.randNum.letter + '-' + data.randNum.number;
          updateCallBox(data.randNum.letter,data.randNum.number);
          // var calledNumberLentgh = data.randNum.calledNumber.length;
          // document.getElementById('totalCall').textContent = calledNumberLentgh;
          // document.getElementById('currentCallColor').style.background = data.randNum.color;
          if (data.randNum.number === "Over"){
            document.getElementById('totalCall').textContent = data.randNum.calledNumber.length - 1;
            localStorage.clear();
            let winningBoardData = data.randNum.board;
            let calledNumber = data.randNum.calledNumber;
            let winPatter = data.randNum.winPattern;
            let winnerName = data.randNum.winnerName
            let winnerBoardNumber = data.randNum.winnerBoardNumber
            let winnerId = data.randNum.winnerId
            let bonus = data.randNum.bonus
          
            const winnerText = document.querySelector('.congrats-text');
            
            if (user_id.toString() === winnerId.toString()){
              winnerText.innerHTML = "You have won the game";
              winnerText.style.color = 'green';
              if (bonus){
                const bonusText = document.querySelector('.bonus-text');
                bonusText.innerHTML = "You have won a " + bonus + "ETB bonus";
                bonusText.style.color = 'green';
              }
              
            }else{
              winnerText.innerHTML = '<span class="badge bg-success">' + winnerName + "</span> has won the game";
              if (bonus){
                const bonusText = document.querySelector('.bonus-text');
                bonusText.innerHTML = '<span class="badge bg-success">' + winnerName + "</span> has won a " + bonus + "ETB bonus";
              }
            }
            const winnerCallBox = document.querySelector('.winner-call-box');
            winnerCallBox.textContent = '';
            for (let i = 0; i < 5; i++) {
              for (let j = 0; j < 5; j++) {
                const callCell = document.createElement('div');
                callCell.classList.add('call-cell');
                callCell.textContent = winningBoardData[i][j];
                if (calledNumber.includes(winningBoardData[i][j])) {
                  if (winPatter.includes(winningBoardData[i][j])) {
                      callCell.style.backgroundColor = 'green';
                  } else {
                      callCell.style.backgroundColor = '#FF6701';
                  }
                } 
                winnerCallBox.appendChild(callCell);
              }
            }
            document.getElementById('winNum').textContent = winnerBoardNumber
            var myModal = new bootstrap.Modal(document.getElementById('bingoModal'));
            myModal.show();
          }else if (data.randNum.number === "Number"){
              localStorage.clear();
              setTimeout(function() {
                  window.location.href = "/bingo/choose-board?" + token;
              }, 3000);
              
          }else{
            document.getElementById('totalCall').textContent = data.randNum.calledNumber.length;
            var lastNumbers = data.randNum.calledNumber.slice(-5);
            const lastNumbersDisplay = document.getElementById('lastNumbers');
            lastNumbersDisplay.textContent = '';
            for (let i = lastNumbers.length - 1; i >= 0; i--) {
                const span = document.createElement('span');
                span.textContent = lastNumbers[i].letter + lastNumbers[i].number;
                span.classList.add('last-5-call-grid-item');
                // span.classList.add('numberCircle');
                span.style.backgroundColor = lastNumbers[i].color;
                span.style.color = lastNumbers[i].text; 
                lastNumbersDisplay.appendChild(span);
            }
          }
      }

  // generate 75grid
  const numberGridContainer = document.querySelector('.number-grid-container');
  for (let i = 0; i < 15; i++) {
    for (let j = 0; j < 5; j++) {
        const value = i + j * 15 + 1;
        const numberGridItem = document.createElement('div');
        numberGridItem.classList.add('number-grid-item');
        numberGridItem.textContent = value;
        numberGridContainer.appendChild(numberGridItem);
    }
  }

  // generate bingo grid board
  const callBox = document.querySelector('.call-box');
  for (let i = 0; i < 5; i++) {
    for (let j = 0; j < 5; j++) {
      const callCell = document.createElement('div');
      callCell.classList.add('call-cell');
      callCell.textContent = gameData['board'][i][j];
      if (callCell.textContent === "*"){
        callCell.style.backgroundColor = 'green';
      }
      callBox.appendChild(callCell);
    }
  }

  // get bingo board value from local storage
  const gridItems = document.querySelectorAll('.call-cell');
  const storedSelectedItems = localStorage.getItem('selectedItems');
  let selectedItems = [];
  let clicked = null;
  if (storedSelectedItems) {
    selectedItems = JSON.parse(storedSelectedItems);
    
    selectedItems.forEach(localItemId => {
      gridItems.forEach(itemId => {
        if (localItemId === itemId.textContent){
          clicked = itemId.dataset.clicked = 'true';
          itemId.style.backgroundColor = 'green';
        }
      });
    });
  }

  //bingo grid board click update
  gridItems.forEach(item => {
    item.addEventListener('click', function selectGridItem(event) {
      const selectedItem = event.target;
      clicked = selectedItem.dataset.clicked === 'true';
      if (selectedItem.textContent === "*"){

      }else if (!clicked) {
        selectedItem.style.backgroundColor = 'green'; // Change color as needed
        selectedItem.dataset.clicked = 'true';

        selectedItems.push(selectedItem.textContent);
        localStorage.setItem('selectedItems',JSON.stringify(selectedItems) );
      }else{
        item.style.backgroundColor = '';
        item.dataset.clicked = 'false';

        const itemIndex = selectedItems.indexOf(selectedItem.textContent);
        selectedItems.splice(itemIndex, 1);
        localStorage.setItem('selectedItems', JSON.stringify(selectedItems));
      }
    });
  });

  //generate 75grid call box on page load
  const numberGridItems = document.querySelectorAll('.number-grid-item');
  if (called_number){
    called_number[1].forEach(callNum => {
      numberGridItems.forEach(item => {
        if (item.textContent.toString() === callNum.toString()) {
            item.style.backgroundColor = '#FF6701';
        }
      });
    });
  }else{
    called_number = ""
  }
  
  // Function to update numbers in 75grid call box and apply animation
  function updateCallBox(letter, number) {
    const current_call = document.getElementById('currentCall');
    
    numberGridItems.forEach(item => {
      if (item.textContent.toString() === number.toString()) {
          item.style.backgroundColor = 'green';
          item.classList.add('blink-animation');
      } else if (item.style.backgroundColor === 'green') {
          item.style.backgroundColor = '#FF6701';
          item.classList.remove('blink-animation');
      } 
    });
  }

  //socket reconnect function
  function reconnectWebSocket() {
    if (!socket || socket.readyState === WebSocket.CLOSED) {
        console.log("Attempting to reconnect WebSocket...");
        try {
            socket = new WebSocket(socket_url);
        } catch (error) {
            console.error("Error occurred during WebSocket reconnect:", error);
            // Handle reconnect error (e.g., retry with backoff strategy)
        }
    }
  }
  // socket on messages
  
    
    // active-call-number
};

  // socket on close
  socket.onclose = function(event) {
    console.log("WebSocket is closed now. Reconnecting...");
    setTimeout(function() {
      reconnectWebSocket();
    }, 3000);
  };

  socket.onerror = function(error) {
    console.error("WebSocket error observed:", error);
    setTimeout(function() {
      reconnectWebSocket();
    }, 3000);
  };

  // game leave
  function leaveBingoForm(){
    event.preventDefault();
    const csrftoken = document.querySelector("[name=csrfmiddlewaretoken]").value;
    fetch('/bingo/leave-game', {
        method: 'POST',
        headers: {
            'Authorization': user_hash_string,
            'X-CSRFToken': csrftoken
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json(); // Assuming the response is JSON
    })
    .then(data => {
            localStorage.clear();
            window.location.href = "/bingo/choose-board?" + token;
        })
    .catch(error => {
        console.error('Error:', error);
    });
  }

  // refresh the page
  function refreshPage() {
      location.reload();
  }

  // bingo button clicked
  function submitBingoForm() {
    event.preventDefault();
    var bingoButton = document.getElementById("bingoButton");
    bingoButton.disabled = true;

    // Re-enable the button after 3 seconds
    setTimeout(function() {
      bingoButton.disabled = false;
    }, 3000);

    const csrftoken = document.querySelector("[name=csrfmiddlewaretoken]").value;
    fetch('/bingo/game-over', {
      method: 'POST',
      headers: {
          'Authorization': user_hash_string,
          'X-CSRFToken': csrftoken
      },
      body: formData
    })
    .then(response => {
      if (!response.ok) {
          throw new Error('Network response was not ok');
      }
      return response.json(); // Assuming the response is JSON
    })
    .then(data => {
      if (data.status === "kick"){
        localStorage.clear();
        const toastEl = document.querySelector('.toast');
        const toast = new bootstrap.Toast(toastEl);
        const toastBody = toastEl.querySelector('.toast-body');
        toastBody.innerText = "Removed. Clicked bingo without pattern match";
        toast.show();
        setTimeout(function() {
            window.location.href = "/bingo/choose-board?" + token;
        }, 3000);
      }
    })
    .catch(error => {
        console.error('Error:', error);
    });
  }

  var myModal = new bootstrap.Modal(document.getElementById('bingoModal'));
  myModal._element.addEventListener('hidden.bs.modal', function (event) {
      window.location.href = "/bingo/choose-board?" + token;
  });
</script>
