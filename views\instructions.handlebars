<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>የቢንጎ ጨዋታ ህጎች</title>
<style>
    body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        text-align: justify;
        background: radial-gradient(circle 1000px at center, #92e0ff 10%, #4FC3F7 50%, #3a6ad2 90%);
    }

    h1 {
        text-align: center;
        background-color: rgba(255, 255, 255, 0.5);
        padding: 20px;
        border: 2px solid rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 40px;
    }

    .section {
        background-color: rgba(255, 255, 255, 0.5);
        padding: 20px;
        border: 2px solid rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    ul {
        list-style-type: none;
        padding-left: 20px;
    }

    li {
        margin-bottom: 10px;
    }

    /* Additional styling for hover effect on sections */
    .section:hover {
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
    }
</style>
</head>
<body>
    <h1>የቢንጎ ጨዋታ ህጎች</h1>

    <div class="section">
        <strong>መጫወቻ ካርድ</strong>
        <ul>
            <li>ጨዋታውን ለመጀመር ከሚመጣልን ከ1-100 የመጫወቻ ካርድ ውስጥ አንዱን እንመርጣለን</li>
            <li>የመጫወቻ ካርዱ ላይ በቀይ ቀለም የተመረጡ ቁጥሮች የሚያሳዩት መጫወቻ ካርድ በሌላ ተጫዋች መመረጡን ነው</li>
            <li>የመጫወቻ ካርድ ስንነካው ከታች በኩል ካርድ ቁጥሩ የሚይዘዉን መጫወቻ ካርድ ያሳየናል</li>
            <li>ወደ ጨዋታው ለመግባት የምንፈልገዉን ካርድ ከመረጥን በኋላ ከታች በኩል መሃል ላይ Play የሚለዉን ክሊክ በማረግ ወደጨዋታው እንገባለን</li>
            <li>active game የሚለው የሚያሳየን አሁን ላይ እየተካሄዱ ያሉ የጨዋታ ብዛቶችን ነው</li>
        </ul>
    </div>

    <div class="section">
        <strong>ጨዋታ</strong>
        <ul>
            <li>ወደ ጨዋታው ስንገባ በመረጥነው የካርድ ቁትር መሰረት የመጫወቻ ካርድ እናገኛለን</li>
            <li>የመጀመሪያ ተጫዋች ከሆንን ከላይ በቀኝ በኩል wait የሚል ጽሁፍ እናገኛለን</li>
            <li>ተጨማሪ ተጫዋች ሲገባ ከላይ በቀኝ በኩል ጨዋታው ለመጀመር ያለዉን ቀሪ ሴኮንድ መቁጠር ይጀምራል</li>
            <li>ጨዋታው ሲጀምር የተለያዪ ቁትሮች ከ1 እስከ 75 መጥራት ይጀምራል</li>
            <li>የሚጠራው ቁጥር የኛ መጫወቻ ካርድ ዉስጥ ካለ የተጠራዉን ቁጥር ክሊክ በማረግ መምረጥ እንችላለን</li>
            <li>የመረጥነዉን ቁጥር ማጥፋት ከፈለግን መልሰን እራሱን ቁጠር ክሊክ በማረግ ማጥፋት እንችላለን</li>
        </ul>
    </div>

    <div class="section">
        <strong>አሸናፊ</strong>
        <ul>
            <li>ቁጥሮቹ ሲጠሩ ከመጫወቻ ካርዳችን ላይ እየመረጥን ወደጎን ወይም ወደታች ወይም ወደሁለቱም አግዳሚ ወይም አራቱን ማእዘናት ከመረጥን ወዲአዉኑ ከታች በኩል bingo የሚለዉን በመንካት ማሸነፍ እንችላለን</li>
            <li>ወደጎን ወይም ወደታች ወዪም ወደሁለቱም አግዳሚ ወይም አራቱን ማእዘናት ሳይጠሩ bingo የሚለዉን ክሊክ ካደረግን ከጨዋታው እንባረራለን</li>
            <li>ሁለት ተጫዋቾች እኩል ቢያሸንፉ ቀድሞ bingo ያለው ተጫዋች አሸናፊ ይሆናል</li>
            <li>ጨዋታው ዉስጥ ከገባን በኋላ ሌላ ተጫዋች ካልገባ leave የሚለዉን በመንካት ከጨዋታው መውጣት እንችላለን</li>
        </ul>
    </div>

</body>
</html>
