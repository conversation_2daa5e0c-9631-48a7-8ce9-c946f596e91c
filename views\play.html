<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Blue  Bingo</title>
  <script src="https://telegram.org/js/telegram-web-app.js?1"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <input type="hidden" name="csrfmiddlewaretoken" value="sLlV8rrwnIMU3eGVqW1GbdpKArTY8a2cKGgkhf0zUQr623xJ0ZuvuxpDRaay5oRv">
  <style>
    html,
    body {
      width: 100%;
      height: 100%;
      margin: 0;
      background: #34a93c;
      display: flex;
      flex-direction: column;

    }

    .body {
      display: flex;
      flex-direction: column;

    }

    .container1 {
      height: auto;
      margin: 0;
      padding: 5px;
      background: #229134;
      box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.10);
      border-top-left-radius: 25px;
      border-top-right-radius: 25px;
      justify-content: center;
      align-items: flex-start;
      gap: 20px;
      display: inline-flex;
    }

    .inner-container {
      flex: 1 1 0;
      flex-direction: column;
      height: 44px;
      padding-top: 10px;
      padding-bottom: 10px;
      background: white;
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25) inset;
      border-radius: 10px;
      justify-content: space-around;
      align-items: center;
      display: flex;
    }

    .text {
      color: #7B499D;
      font-size: 12px;
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

    .amount {
      color: #7B499D;
      font-size: 12px;
      font-family: SF Pro Text;
      font-weight: 700;
      word-wrap: break-word;
    }

    .buttons-container {
      flex-direction: column;
      /* Changed to column */
      justify-content: flex-start;
      align-items: center;
      display: inline-flex;
    }

    .buttons-inner-container {
      flex: 1 1 0;
      padding-left: 20px;
      padding-right: 20px;
      padding-top: 15px;
      padding-bottom: 15px;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      display: flex;
    }

    .button-container {
      justify-content: flex-center;
      align-items: center;
      gap: 20px;
      display: inline-flex;
    }

    .button {
      flex: 1 1 0;
      height: 44px;
      padding-left: 52px;
      padding-right: 52px;
      padding-top: 10px;
      padding-bottom: 10px;
      border-radius: 100px;
      justify-content: center;
      align-items: center;
      border: none;
      display: flex;
    }

    .bingo-button {
      
      margin-bottom: 2rem;
      height: 54px;
      /*background: linear-gradient(0deg, #FF6701 0%, #FF6701 100%),
        radial-gradient(59.56% 59.56% at 44.88% 67.14%, rgba(255, 255, 255, 0.41) 0%, rgba(255, 255, 255, 0) 70%, rgba(255, 255, 255, 0) 100%),
        radial-gradient(65.57% 52.41% at 244.23% 8.59%, rgba(0, 0, 0, 0.23) 0%, rgba(0, 0, 0, 0) 86%);*/
      background-image: linear-gradient(to bottom right, #ffa568 10%, #FF6701 70%);
      width: 100%;
    }

    .reset-button {
      /*background: linear-gradient(0deg, #229ED9 0%, #229ED9 100%),
        radial-gradient(59.56% 59.56% at 44.88% 67.14%, rgba(255, 255, 255, 0.41) 0%, rgba(255, 255, 255, 0) 70%, rgba(255, 255, 255, 0) 100%),
        radial-gradient(65.57% 52.41% at 244.23% 8.59%, rgba(0, 0, 0, 0.23) 0%, rgba(0, 0, 0, 0) 86%);*/
      background-image: linear-gradient(to bottom right, #229ED9 10%, #229ED9 70%);
    }

    .leave-button {
      background-image: linear-gradient(to bottom right, #E64646 10%, #E64646 70%)
      /*background: linear-gradient(0deg, #E64646 0%, #E64646 100%),
        radial-gradient(59.56% 59.56% at 44.88% 67.14%, rgba(255, 255, 255, 0.41) 0%, rgba(255, 255, 255, 0) 70%, rgba(255, 255, 255, 0) 100%),
        radial-gradient(65.57% 52.41% at 244.23% 8.59%, rgba(0, 0, 0, 0.23) 0%, rgba(0, 0, 0, 0) 86%);
      box-shadow: 4px 15px 62px rgba(0, 0, 0, 0.50);*/
    }

    .button-text {
      text-align: center;
      color: #FEFDED;
      font-size: 16px;
      font-family: SF Pro Text;
      font-weight: 600;
    }

    .bingo-button-text {
      text-align: center;
      color: #FEFDED;
      font-size: 20px;
      font-family: SF Pro Text;
      font-weight: 600;
      word-wrap: break-word;
    }

    .grids-container {
      margin: 0.5rem;
      
      height: 70%;
      
      justify-content: center;
      align-items: flex-center;
      gap: 10px;
      display: inline-flex;
      flex-direction: row;
      /* Added column direction */
    }

    .bingo-grid {
      padding-top: 5px;
      padding-bottom: 2%;
      background: rgba(255, 255, 255, 0.20);
      border-radius: 10px;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      gap: 3px;
      display: inline-flex;
    }

    .bingo-grid1 {
      padding: 5px;
      background: rgba(255, 255, 255, 0.20);
      border-radius: 10px;
      flex-direction: column;
      align-items: center;
      align-self: stretch;
      gap: 5px;
      display: inline-flex;

    }

    .color-square-container {
      justify-content: center;
      align-items: flex-start;
      gap: 5px;
      display: inline-flex;
    }

    .color-square-container1 {
      justify-content: center;
      align-items: flex-start;
      gap: 10px;
      display: inline-flex;
    }

    .color-square {
      width: 1.53rem;
      height: 1.53rem;
      border-radius: 5px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px;
      display: inline-flex;
    }

    .color-square1 {
      width: 30px;
      height: 30px;
      border-radius: 20px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px;
      display: inline-flex;
    }

    .color-square-B {
      background: #FAA300;
    }

    .color-square-I {
      background: #5ACD30;
    }

    .color-square-N {
      background: #4B91CA;
    }

    .color-square-G {
      background: #E64646;
    }

    .color-square-O {
      background: #000000;
    }

    .color-square-text {
      text-align: center;
      color: white;
      font-size: 12px;
      font-family: Inter;
      font-weight: 700;
      word-wrap: break-word;
    }

    .color-square-text1 {
     
      text-align: center;
      color: white;
      font-size: 18px;
      font-family: Inter;
      font-weight: 700;
      word-wrap: break-word;
    }

    .number-grid-container {
      display: grid;
      grid-template-columns: repeat(5, auto);
      grid-template-rows: repeat(5, auto);
      justify-content: center;
      align-items: center;
      margin-top: 2px;
    }

    .number-grid-item {
      width: 5px;
      height: 12px;
      padding: 10px;
      background: rgba(255, 255, 255, 0.35);
      border-radius: 5px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 1px;
      display: inline-flex;
      margin-left: 5px;
      margin-right: 5px;
      margin-top: 0.205rem;
      margin-bottom: 0.205rem;
      font-size: 12px;
      /* Increased font size */
      color: #FFFFFF;
      /* Changed text color to black */
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

    .wrapper {
      flex: 1 1 0;
      align-self: stretch;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      gap: 20px;
      display: inline-flex;
    }

    .inner-wrapper {
      align-self: stretch;
      height: 108px;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      gap: 10px;
      display: flex;
    }

    .info-box {
      align-self: stretch;
      padding: 4px;
      background: rgba(255, 255, 255, 0.50);
      border-radius: 10px;
      justify-content: space-between;
      align-items: center;
      display: inline-flex;
    }

    .info-content {
      justify-content: flex-start;
      align-items: center;
      gap: 10px;
      display: flex;
    }

    .info-icon {
      width: 16px;
      height: 16px;
      padding-left: 3px;
      padding-right: 3px;
      padding-top: 1px;
      padding-bottom: 1px;
      justify-content: center;
      align-items: center;
      display: flex;
    }

    .icon {
      width: 10px;
      height: 14px;
      background: #464A54;
    }

    .info-text {
      text-align: center;
      color: #464A54;
      font-size: 15px;
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

    .info-value {
      text-align: center;
      color: #7B499D;
      font-size: 24px;
      font-family: SF Pro Text;
      font-weight: 700;
      word-wrap: break-word;
    }

    .call-grid {
      align-self: stretch;
      flex: 1 1 0;
      padding-top: 10px;
      padding-bottom: 10px;
      background: rgba(255, 255, 255, 0.50);
      border-radius: 10px;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      display: flex;
    }

    .call-box {
      margin-top: 5px;
      display: grid;
      grid-template-columns: repeat(5, auto);
      grid-template-rows: repeat(5, auto);
      justify-content: center;
      align-items: flex-center;
      gap: 4px;
      
    }

    .winner-call-box {
      margin-top: 8px;
      display: grid;
      grid-template-columns: repeat(5, auto);
      grid-template-rows: repeat(5, auto);
      justify-content: center;
      align-items: flex-center;
      gap: 4px;
      
    }

    .call-cell {
      width: 35px;
      height: 35px;
      padding: 3px;
      background: rgba(255, 255, 255, 0.50);
      border-radius: 5px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px;
      display: inline-flex;

      text-align: center;
      color: #464A54;
      font-size: 18px;
      font-weight: 700;
      word-wrap: break-word;
    }
    
    .current-call {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60px;
      height: 60.72px;
      text-align: center;
      background: linear-gradient(0deg, #FF6701 0%, #FF6701 100%), radial-gradient(59.56% 59.56% at 44.88% 67.14%, rgba(255, 255, 255, 0.41) 0%, rgba(255, 255, 255, 0) 70%, rgba(255, 255, 255, 0) 100%), radial-gradient(74.77% 36.11% at 84.19% 33.79%, rgba(255, 114.75, 114.75, 0.23) 0%, rgba(93.50, 46.36, 46.36, 0) 86%);
      box-shadow: 1.4457833766937256px 6px 22.409643173217773px rgba(0, 0, 0, 0.20);
      border-radius: 30px;
    }

    .current-call-span {
      text-align: center;
      color: white;
      font-size: 20px;
      font-family: SF Pro Text;
      font-weight: 700;
      word-wrap: break-word;
    }

    .last-5-info-box {
      padding: 12px;
      background: rgba(255, 255, 255, 0.50);
      border-radius: 10px;
      justify-content: space-between;
      justify-content: center;
      align-items: center;
    }

    .last-5-call-grid-item {
      width: 5px;
      height: 12px;
      padding: 12px;
      background: rgba(255, 255, 255, 0.35);
      border-radius: 50px;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 1%;
      display: inline-flex;
      margin-left: 3px;
      margin-right: 3px;
      font-size: 10px;
      /* Increased font size */
      color: #FFFFFF;
      /* Changed text color to black */
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

    .inner-content {
      align-self: stretch;
      height: 149px;
      padding-top: 20px;
      padding-bottom: 20px;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      gap: 10px;
      display: flex;
    }

    /* Bingo box styling */
    .bingo-box {
      align-self: stretch;
      height: 10rem;
      padding-left: 20px;
      padding-right: 20px;
      padding-top: 5px;
      padding-bottom: 5px;
      background: linear-gradient(90deg, #FEA82F, #FF6701);
      border-radius: 20px;
      border: 2px solid white;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 5px;
      display: flex;
    }

    /* Text styling */
    .bingo-text {
      text-align: center;
      color: white;
      font-size: 36px;
      font-family: SF Pro Text;
      font-weight: 600;
      padding-bottom: 5px;
      word-wrap: break-word;
    }

    .congrats-text {
      align-self: stretch;
      text-align: center;
      color: white;
      font-size: 20px;
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

    .bonus-text {
      align-self: stretch;
      text-align: center;
      color: white;
      font-size: 16px;
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

    .winner-text {
      align-self: stretch;
      text-align: center;
      color: white;
      font-size: 12px;
      font-family: SF Pro Text;
      font-weight: 500;
      word-wrap: break-word;
    }

     /* Blink animation */
     @keyframes blink {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

    .blink-animation {
        animation: blink 1s infinite;
    }

    
  </style>
</head>

<body>
  <!-- Modal -->
  <div class="modal text-center" id="bingoModal" tabindex="-1" aria-labelledby="bingoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content" style="background:#AD88C6;">
        <div class="inner-content text-center">
          <div class="bingo-box">
            <div class="bingo-text">BINGO!</div>
            <div class="congrats-text"></div>
            <div class="bonus-text"></div>
          </div>
        </div>
        
        <div class="modal-body ">
          <div class="bingo-grid1">
            <div class="color-square-container1">
              <div class="color-square1 color-square-B">
                <div class="color-square-text1">B</div>
              </div>
              <div class="color-square1 color-square-I">
                <div class="color-square-text1">I</div>
              </div>
              <div class="color-square1 color-square-N">
                <div class="color-square-text1">N</div>
              </div>
              <div class="color-square1 color-square-G">
                <div class="color-square-text1">G</div>
              </div>
              <div class="color-square1 color-square-O">
                <div class="color-square-text1">O</div>
              </div>
            </div>
            <div class="modal-body winner-call-box" id="winningBoard"></div>
            <div class="winner-text">Board number  <span id="winNum">-</span></div>
          </div>
        </div>
        
        <div class="text-center">
          <div class="buttons-inner-container">
            <button class="bingo-button button" data-bs-dismiss="modal">
              <div class="bingo-button-text">Play Again</div>
            </button>
          </div>
          <div
            style="text-align: center; color: #FFFEF5; font-size: 12px; font-family: SF Pro Text; font-weight: 400; word-wrap: break-word">
            © Blue Bingo 2024</div>
        <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> -->
        </div>
    </div>
    </div>
  </div>

  <div class="container-fluid">
    <div class="row">
        <div class="col">
          <div class="body">
            <div class="container1 mt-1">
              <div class="inner-container">
                <div class="text">GAME</div>
                  <div class="amount"><span>43087</span></div>
              </div>
              <div class="inner-container">
                <div class="text">DERASH</div>
                
                  <div class="amount"><span id="derash">-</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">Bonus</div>
                
                <div id="bonus" class="amount"><span>-</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">Players</div>
                
                  <div class="amount"><span id="totalPlayers">-</span></div>
                
              </div>
              <div class="inner-container">
                <div class="text">Bet</div>
                <div class="amount"><span>10</span></div>
              </div>
              <div class="inner-container">
                <div class="text">call</div>
                <div class="amount"><span id="totalCall">0</span></div>
              </div>
            </div>

            <div class="grids-container">
              <div class="bingo-grid">
                <div class="color-square-container">
                  <div class="color-square color-square-B">
                    <div class="color-square-text">B</div>
                  </div>
                  <div class="color-square color-square-I">
                    <div class="color-square-text">I</div>
                  </div>
                  <div class="color-square color-square-N">
                    <div class="color-square-text">N</div>
                  </div>
                  <div class="color-square color-square-G">
                    <div class="color-square-text">G</div>
                  </div>
                  <div class="color-square color-square-O">
                    <div class="color-square-text">O</div>
                  </div>
                </div>
                <div class="number-grid-container">
                </div>
              </div>
              <div class="wrapper">
                <div class="inner-wrapper">
                  <div class="info-box">
                    <div class="info-content">
                      <div class="info-text">Count Down</div>
                    </div>
                    
                      <div class="info-value"><span id="countdown">Wait</span></div>
                    
                  </div>
                  <div
                    style="align-self: stretch; height: 51px; padding-left: 15px; padding-right: 15px; padding-top: 5px; padding-bottom: 5px; background: #7B499D; border-radius: 20px; justify-content: space-between; align-items: center; display: inline-flex">
                    <div
                      style="text-align: center; color: white; font-size: 15px; font-family: SF Pro Text; font-weight: 700; word-wrap: break-word ">
                      Current Call
                    </div>
                    <div>
                      <div id="currentCallColor" class="current-call">
                        <span id="currentCall" class="current-call-span">-</span>
                      </div>
                    </div>
                      
                  </div>
                  <div id="lastNumbers" class="last-5-info-box">
                    <span class="last-5-call-grid-item">-</span>
                  </div>
                  <div class="bingo-grid1">
                    <div class="color-square-container1">
                      <div class="color-square1 color-square-B">
                        <div class="color-square-text1">B</div>
                      </div>
                      <div class="color-square1 color-square-I">
                        <div class="color-square-text1">I</div>
                      </div>
                      <div class="color-square1 color-square-N">
                        <div class="color-square-text1">N</div>
                      </div>
                      <div class="color-square1 color-square-G">
                        <div class="color-square-text1">G</div>
                      </div>
                      <div class="color-square1 color-square-O">
                        <div class="color-square-text1">O</div>
                      </div>
                    </div>
                    <div class="call-box"></div>
                    <div class="winner-text">Board number  <span id="winNum">11</span></div>
                  </div>
                </div>

              </div>

            </div>
            <div class="buttons-container">
              <div class="buttons-inner-container">
                <button id="bingoButton" class="bingo-button button" onclick="submitBingoForm()">
                  <div class="bingo-button-text">BINGO!</div>
                </button>
                <div class="button-container">
                  <button class="reset-button button" onclick="refreshPage()">
                    <div class="bingo-button-text">Refresh</div>
                  </button>
                  <button class="leave-button button" onclick="leaveBingoForm()">
                    <div class="bingo-button-text">Leave</div>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>

  <!-- Add a container for the notifications -->
  <div class="position-fixed top-0 end-0 p-3" style="z-index: 11">
    <div class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
          <div class="toast-body">
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  </div>
</body>
<script id="token" type="application/json">"id=408231241\u0026stake=10\u0026auth_date=1722726123\u0026hash=6ecae598627cf59f645b6003b1a33cffa15b13cbeab4ae870bb1a3e14545582c"</script>
<script id="socket_url" type="application/json">"ws://addisbingobot.com/ws/game/game43087/?user_id=408231241"</script>
<script id="gameData" type="application/json">{"board": [[11, 21, 44, 49, 64], [4, 28, 34, 55, 62], [2, 26, "*", 47, 71], [14, 29, 41, 48, 73], [5, 24, 31, 51, 63]], "lastCalledNumber": []}</script>
<script id="called_number" type="application/json">null</script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
    var token = JSON.parse(document.getElementById('token').textContent);
    var socket_url = JSON.parse(document.getElementById('socket_url').textContent);
    var gameData = JSON.parse(document.getElementById('gameData').textContent);
    var called_number = JSON.parse(document.getElementById('called_number').textContent);
    let socket;
 
    Telegram.WebApp.ready();
    const user_hash_string = Telegram.WebApp.initData;
    const user_id = Telegram.WebApp.initDataUnsafe.user.id;
    const csrftoken = document.querySelector("[name=csrfmiddlewaretoken]").value;

//   const socket = new WebSocket(socket_url + "/ws/game/" + game_room + "/?user_id=" + user_id);
    socket = new WebSocket(socket_url);

    const formData = new FormData();
    formData.append('user_id', user_id);
    formData.append('user_hash', user_hash_string);

  // generate 75grid
  const numberGridContainer = document.querySelector('.number-grid-container');
  for (let i = 0; i < 15; i++) {
    for (let j = 0; j < 5; j++) {
        const value = i + j * 15 + 1;
        const numberGridItem = document.createElement('div');
        numberGridItem.classList.add('number-grid-item');
        numberGridItem.textContent = value;
        numberGridContainer.appendChild(numberGridItem);
    }
  }

  // generate bingo grid board
  const callBox = document.querySelector('.call-box');
  for (let i = 0; i < 5; i++) {
    for (let j = 0; j < 5; j++) {
      const callCell = document.createElement('div');
      callCell.classList.add('call-cell');
      callCell.textContent = gameData['board'][i][j];
      if (callCell.textContent === "*"){
        callCell.style.backgroundColor = 'green';
      }
      callBox.appendChild(callCell);
    }
  }

  // get bingo board value from local storage
  const gridItems = document.querySelectorAll('.call-cell');
  const storedSelectedItems = localStorage.getItem('selectedItems');
  let selectedItems = [];
  let clicked = null;
  if (storedSelectedItems) {
    selectedItems = JSON.parse(storedSelectedItems);
    
    selectedItems.forEach(localItemId => {
      gridItems.forEach(itemId => {
        if (localItemId === itemId.textContent){
          clicked = itemId.dataset.clicked = 'true';
          itemId.style.backgroundColor = 'green';
        }
      });
    });
  }

  //bingo grid board click update
  gridItems.forEach(item => {
    item.addEventListener('click', function selectGridItem(event) {
      const selectedItem = event.target;
      clicked = selectedItem.dataset.clicked === 'true';
      if (selectedItem.textContent === "*"){

      }else if (!clicked) {
        selectedItem.style.backgroundColor = 'green'; // Change color as needed
        selectedItem.dataset.clicked = 'true';

        selectedItems.push(selectedItem.textContent);
        localStorage.setItem('selectedItems',JSON.stringify(selectedItems) );
      }else{
        item.style.backgroundColor = '';
        item.dataset.clicked = 'false';

        const itemIndex = selectedItems.indexOf(selectedItem.textContent);
        selectedItems.splice(itemIndex, 1);
        localStorage.setItem('selectedItems', JSON.stringify(selectedItems));
      }
    });
  });

  //generate 75grid call box on page load
  const numberGridItems = document.querySelectorAll('.number-grid-item');
  if (called_number){
    called_number[1].forEach(callNum => {
      numberGridItems.forEach(item => {
        if (item.textContent.toString() === callNum.toString()) {
            item.style.backgroundColor = '#FF6701';
        }
      });
    });
  }else{
    called_number = ""
  }
  
  // Function to update numbers in 75grid call box and apply animation
  function updateCallBox(letter, number) {
    const current_call = document.getElementById('currentCall');
    
    numberGridItems.forEach(item => {
      if (item.textContent.toString() === number.toString()) {
          item.style.backgroundColor = 'green';
          item.classList.add('blink-animation');
      } else if (item.style.backgroundColor === 'green') {
          item.style.backgroundColor = '#FF6701';
          item.classList.remove('blink-animation');
      } 
    });
  }

  //socket reconnect function
  function reconnectWebSocket() {
    if (!socket || socket.readyState === WebSocket.CLOSED) {
        console.log("Attempting to reconnect WebSocket...");
        try {
            socket = new WebSocket(socket_url);
        } catch (error) {
            console.error("Error occurred during WebSocket reconnect:", error);
            // Handle reconnect error (e.g., retry with backoff strategy)
        }
    }
  }
  // socket on messages
  socket.onmessage = function(event) {
    event.preventDefault();
    const data = JSON.parse(event.data);
    if (data.gameState === "countdown"){
        document.getElementById('countdown').textContent = data.countdown;
    }
    else if (data.gameState === "derash"){
        document.getElementById('derash').textContent = data.derash;
        document.getElementById('totalPlayers').textContent = data.totalPlayers;
        if (data.bonus){
          document.getElementById('bonus').textContent = data.bonus;
        }
    }else{
        document.getElementById('currentCall').textContent = data.randNum.letter + '-' + data.randNum.number;
        updateCallBox(data.randNum.letter,data.randNum.number);
        // var calledNumberLentgh = data.randNum.calledNumber.length;
        // document.getElementById('totalCall').textContent = calledNumberLentgh;
        // document.getElementById('currentCallColor').style.background = data.randNum.color;
        if (data.randNum.number === "Over"){
          document.getElementById('totalCall').textContent = data.randNum.calledNumber.length - 1;
          localStorage.clear();
          let winningBoardData = data.randNum.board;
          let calledNumber = data.randNum.calledNumber;
          let winPatter = data.randNum.winPattern;
          let winnerName = data.randNum.winnerName
          let winnerBoardNumber = data.randNum.winnerBoardNumber
          let winnerId = data.randNum.winnerId
          let bonus = data.randNum.bonus
        
          const winnerText = document.querySelector('.congrats-text');
          
          if (user_id.toString() === winnerId.toString()){
            winnerText.innerHTML = "You have won the game";
            winnerText.style.color = 'green';
            if (bonus){
              const bonusText = document.querySelector('.bonus-text');
              bonusText.innerHTML = "You have won a " + bonus + "ETB bonus";
              bonusText.style.color = 'green';
            }
            
          }else{
            winnerText.innerHTML = '<span class="badge bg-success">' + winnerName + "</span> has won the game";
            if (bonus){
              const bonusText = document.querySelector('.bonus-text');
              bonusText.innerHTML = '<span class="badge bg-success">' + winnerName + "</span> has won a " + bonus + "ETB bonus";
            }
          }
          const winnerCallBox = document.querySelector('.winner-call-box');
          winnerCallBox.textContent = '';
          for (let i = 0; i < 5; i++) {
            for (let j = 0; j < 5; j++) {
              const callCell = document.createElement('div');
              callCell.classList.add('call-cell');
              callCell.textContent = winningBoardData[i][j];
              if (calledNumber.includes(winningBoardData[i][j])) {
                if (winPatter.includes(winningBoardData[i][j])) {
                    callCell.style.backgroundColor = 'green';
                } else {
                    callCell.style.backgroundColor = '#FF6701';
                }
              } 
              winnerCallBox.appendChild(callCell);
            }
          }
          document.getElementById('winNum').textContent = winnerBoardNumber
          var myModal = new bootstrap.Modal(document.getElementById('bingoModal'));
          myModal.show();
        }else if (data.randNum.number === "Number"){
            localStorage.clear();
            setTimeout(function() {
                window.location.href = "/choose-board?" + token;
            }, 3000);
            
        }else{
          document.getElementById('totalCall').textContent = data.randNum.calledNumber.length;
          var lastNumbers = data.randNum.calledNumber.slice(-5);
          const lastNumbersDisplay = document.getElementById('lastNumbers');
          lastNumbersDisplay.textContent = '';
          for (let i = lastNumbers.length - 1; i >= 0; i--) {
              const span = document.createElement('span');
              span.textContent = lastNumbers[i].letter + lastNumbers[i].number;
              span.classList.add('last-5-call-grid-item');
              // span.classList.add('numberCircle');
              span.style.backgroundColor = lastNumbers[i].color;
              span.style.color = lastNumbers[i].text; 
              lastNumbersDisplay.appendChild(span);
          }
        }
    }
    
    // active-call-number
};

  // socket on close
  socket.onclose = function(event) {
    console.log("WebSocket is closed now. Reconnecting...");
    setTimeout(function() {
      reconnectWebSocket();
    }, 3000);
  };

  socket.onerror = function(error) {
    console.error("WebSocket error observed:", error);
    setTimeout(function() {
      reconnectWebSocket();
    }, 3000);
  };

  // game leave
  function leaveBingoForm(){
    event.preventDefault();
    const csrftoken = document.querySelector("[name=csrfmiddlewaretoken]").value;
    fetch('/bingo/leave-game', {
        method: 'POST',
        headers: {
            'Authorization': user_hash_string,
            'X-CSRFToken': csrftoken
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json(); // Assuming the response is JSON
    })
    .then(data => {
            localStorage.clear();
            window.location.href = "/choose-board?" + token;
        })
    .catch(error => {
        console.error('Error:', error);
    });
  }

  // refresh the page
  function refreshPage() {
      location.reload();
  }

  // bingo button clicked
  function submitBingoForm() {
    event.preventDefault();
    var bingoButton = document.getElementById("bingoButton");
    bingoButton.disabled = true;

    // Re-enable the button after 3 seconds
    setTimeout(function() {
      bingoButton.disabled = false;
    }, 3000);

    const csrftoken = document.querySelector("[name=csrfmiddlewaretoken]").value;
    fetch('/bingo/game-over', {
      method: 'POST',
      headers: {
          'Authorization': user_hash_string,
          'X-CSRFToken': csrftoken
      },
      body: formData
    })
    .then(response => {
      if (!response.ok) {
          throw new Error('Network response was not ok');
      }
      return response.json(); // Assuming the response is JSON
    })
    .then(data => {
      if (data.status === "kick"){
        localStorage.clear();
        const toastEl = document.querySelector('.toast');
        const toast = new bootstrap.Toast(toastEl);
        const toastBody = toastEl.querySelector('.toast-body');
        toastBody.innerText = "Removed. Clicked bingo without pattern match";
        toast.show();
        setTimeout(function() {
            window.location.href = "/choose-board?" + token;
        }, 3000);
      }
    })
    .catch(error => {
        console.error('Error:', error);
    });
  }

  var myModal = new bootstrap.Modal(document.getElementById('bingoModal'));
  myModal._element.addEventListener('hidden.bs.modal', function (event) {
      window.location.href = "/choose-board?" + token;
  });
</script>

</html>